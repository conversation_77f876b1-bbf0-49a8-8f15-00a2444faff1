# Internationalization (i18n) Setup

This project now uses **route-based internationalization** to avoid hydration issues and provide better SEO.

## How it works

- **English routes**: `/en/*` (e.g., `/en/doctors`, `/en/clinics`)
- **Arabic routes**: `/ar/*` (e.g., `/ar/doctors`, `/ar/clinics`)
- **Default redirect**: `/` → `/en`

## Key Features

### 1. Route-based Language Switching
- No client-side language switching that causes hydration issues
- Each language has its own URL structure
- SEO-friendly with proper language-specific URLs

### 2. Automatic Language Detection
- Middleware detects user's preferred language from browser headers
- Redirects to appropriate language route on first visit
- Supports manual language switching via the language toggle

### 3. RTL Support
- Automatic RTL layout for Arabic (`/ar/*` routes)
- Proper text direction and layout adjustments
- Font switching between Inter (Latin) and Cairo (Arabic)

## File Structure

```
app/
├── layout.tsx              # Root layout
├── page.tsx               # Root redirect to /en
├── globals.css            # Global styles
├── [locale]/              # Locale-specific routes
│   ├── layout.tsx         # Locale layout with language provider
│   ├── page.tsx           # Home page
│   ├── doctors/           # Doctors pages
│   ├── clinics/           # Clinics pages
│   ├── auth/              # Auth pages
│   └── about/             # About pages
```

## Usage

### In Components
```tsx
import { useLanguage } from "@/components/providers/language-provider"

export function MyComponent() {
  const { t, language, switchLanguage, isRTL } = useLanguage()
  
  return (
    <div>
      <h1>{t("hero.title")}</h1>
      <button onClick={() => switchLanguage("ar")}>
        Switch to Arabic
      </button>
    </div>
  )
}
```

### Navigation Links
```tsx
import Link from "next/link"
import { useLanguage } from "@/components/providers/language-provider"

export function Navigation() {
  const { language } = useLanguage()
  
  return (
    <Link href={`/${language}/doctors`}>
      Doctors
    </Link>
  )
}
```

## Configuration

### Next.js Config (`next.config.mjs`)
```javascript
i18n: {
  locales: ['en', 'ar'],
  defaultLocale: 'en',
  localeDetection: true,
}
```

### Middleware (`middleware.ts`)
- Handles automatic language detection
- Redirects to appropriate locale routes
- Preserves path structure when switching languages

## Benefits

1. **No Hydration Issues**: Language is determined server-side
2. **Better SEO**: Each language has its own URL
3. **Improved Performance**: No client-side language switching
4. **Better UX**: URLs reflect the current language
5. **Proper RTL Support**: Automatic layout adjustments for Arabic

## Adding New Translations

Add new translation keys to the `translations` object in `components/providers/language-provider.tsx`:

```tsx
const translations = {
  en: {
    "new.key": "English text",
    // ... more translations
  },
  ar: {
    "new.key": "النص العربي",
    // ... more translations
  }
}
``` 