@tailwind base;
@tailwind components;
@tailwind utilities;

@custom-variant dark (&:is(.dark *));

:root {
  --background: 255 255 255;
  --foreground: 15 23 42;
  --card: 255 255 255;
  --card-foreground: 15 23 42;
  --popover: 255 255 255;
  --popover-foreground: 15 23 42;
  --primary: 59 130 246;
  --primary-foreground: 255 255 255;
  --secondary: 241 245 249;
  --secondary-foreground: 51 65 85;
  --muted: 248 250 252;
  --muted-foreground: 100 116 139;
  --accent: 241 245 249;
  --accent-foreground: 51 65 85;
  --destructive: 239 68 68;
  --destructive-foreground: 255 255 255;
  --border: 226 232 240;
  --input: 226 232 240;
  --ring: 59 130 246;
  --radius: 12px;
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground font-sans antialiased;
    font-feature-settings: "rlig" 1, "calt" 1;
  }

  /* Smooth scrolling */
  html {
    scroll-behavior: smooth;
  }

  /* Prevent flash of untranslated content */
  .no-flash {
    opacity: 0;
    transition: opacity 0.2s ease-in-out;
  }

  .no-flash.loaded {
    opacity: 1;
  }
}

/* Custom animations */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.95);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

.animate-fade-in-up {
  animation: fadeInUp 0.6s ease-out;
}

.animate-slide-in-right {
  animation: slideInRight 0.5s ease-out;
}

.animate-scale-in {
  animation: scaleIn 0.3s ease-out;
}

/* Gradient backgrounds */
.gradient-bg {
  background: linear-gradient(135deg, rgb(59 130 246 / 0.1) 0%, rgb(147 197 253 / 0.1) 100%);
}

.gradient-card {
  background: linear-gradient(135deg, rgb(255 255 255) 0%, rgb(248 250 252) 100%);
}

/* Glass morphism effect */
.glass {
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

/* Hover effects */
.hover-lift {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.hover-lift:hover {
  transform: translateY(-8px);
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.15);
}

/* RTL Support */
[dir="rtl"] {
  direction: rtl;
}

[dir="rtl"] .font-sans {
  font-family: var(--font-cairo), system-ui, sans-serif;
}

/* Mobile-first responsive utilities */
.mobile-search {
  @apply fixed bottom-0 left-0 right-0 bg-white border-t border-gray-200 p-4 md:relative md:border-0 md:p-0 md:bg-transparent;
}

/* Custom scrollbar */
.custom-scrollbar::-webkit-scrollbar {
  width: 6px;
}

.custom-scrollbar::-webkit-scrollbar-track {
  background: rgb(241 245 249);
  border-radius: 3px;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
  background: rgb(148 163 184);
  border-radius: 3px;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
  background: rgb(100 116 139);
}


