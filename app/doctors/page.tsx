"use client"

import { useQuery } from "convex/react"
import { api } from "@/convex/_generated/api"
import { <PERSON><PERSON><PERSON> } from "@/components/ui/doctor-card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Search, Filter, ArrowLeft } from "lucide-react"
import Link from "next/link"
import { useState, useMemo } from "react"
import { ErrorBoundary } from "@/components/ui/error-boundary"
import { getDoctorFullName } from "@/lib/utils"

export default function DoctorsPage() {
  const doctors = useQuery(api.doctors.getAllDoctors)
  const [searchTerm, setSearchTerm] = useState("")
  const [selectedSpecialty, setSelectedSpecialty] = useState("")

  // Get unique specialties - memoized to prevent recalculation
  const specialties = useMemo(() => {
    if (!doctors) return []
    const uniqueSpecialties = [...new Set(doctors.map(doc => doc.specialty))]
    return uniqueSpecialties.sort()
  }, [doctors])

  // Filter doctors based on search and specialty - memoized to prevent recalculation
  const filteredDoctors = useMemo(() => {
    if (!doctors) return []
    
    const searchLower = searchTerm.toLowerCase()
    const specialtyLower = selectedSpecialty.toLowerCase()
    
    return doctors.filter(doctor => {
      const matchesSearch = !searchTerm || 
                           getDoctorFullName(doctor).toLowerCase().includes(searchLower) ||
                           doctor.specialty.toLowerCase().includes(searchLower) ||
                           (doctor.clinic_name && doctor.clinic_name.toLowerCase().includes(searchLower))
      
      const matchesSpecialty = !selectedSpecialty || doctor.specialty === selectedSpecialty
      
      return matchesSearch && matchesSpecialty
    })
  }, [doctors, searchTerm, selectedSpecialty])

  if (doctors === undefined) {
    return (
      <div className="min-h-screen bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="text-center">
            <div className="flex flex-col items-center space-y-4">
              <img
                src="/Doctor-Symbol-Preloader.gif"
                alt="Loading..."
                className="w-20 h-20"
              />
              <div className="text-gray-600 text-sm font-medium">
                Loading Doctors...
              </div>
            </div>
          </div>
        </div>
      </div>
    )
  }

  // Handle empty doctors array
  if (doctors.length === 0) {
    return (
      <div className="min-h-screen bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="text-center">
            <div className="inline-flex items-center px-4 py-2 bg-yellow-50 rounded-full text-yellow-700 text-sm font-medium">
              No doctors available at the moment
            </div>
          </div>
        </div>
      </div>
    )
  }

  return (
    <ErrorBoundary>
      <div className="min-h-screen bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          {/* Header */}
          <div className="mb-8">
            <Link href="/" className="inline-flex items-center text-blue-600 hover:text-blue-700 mb-4">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Home
            </Link>
            
            <h1 className="text-4xl font-bold text-gray-900 mb-4">
              All Doctors
            </h1>
            
            <p className="text-xl text-gray-600">
              Find and connect with healthcare professionals
            </p>
          </div>

          {/* Search and Filters */}
          <div className="bg-white rounded-2xl shadow-lg p-6 mb-8">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                              <Input
                placeholder="Search doctors, specialties, or clinics..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
              </div>
              
              <Select value={selectedSpecialty} onValueChange={setSelectedSpecialty}>
                <SelectTrigger>
                  <Filter className="h-4 w-4 mr-2" />
                  <SelectValue placeholder="All Specialties" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="">All Specialties</SelectItem>
                  {specialties.map((specialty) => (
                    <SelectItem key={specialty} value={specialty}>
                      {specialty}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              
              <div className="text-sm text-gray-500 flex items-center justify-center">
                {filteredDoctors.length} doctor{filteredDoctors.length !== 1 ? 's' : ''} found
              </div>
            </div>
          </div>

          {/* Doctors Grid */}
          {filteredDoctors.length > 0 ? (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
              {filteredDoctors.map((doctor) => (
                <div key={doctor._id} className="animate-fade-in-up">
                  <DoctorCard doctor={doctor} />
                </div>
              ))}
            </div>
          ) : (
            <div className="text-center py-12">
              <div className="text-gray-500 text-lg mb-4">
                No doctors found matching your criteria
              </div>
              <Button
                variant="outline"
                onClick={() => {
                  setSearchTerm("")
                  setSelectedSpecialty("")
                }}
              >
                Clear Filters
              </Button>
            </div>
          )}
        </div>
      </div>
    </ErrorBoundary>
  )
}
