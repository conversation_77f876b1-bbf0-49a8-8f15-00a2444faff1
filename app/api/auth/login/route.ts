import { NextRequest, NextResponse } from "next/server";
import { ConvexHttpClient } from "convex/browser";
import { api } from "@/convex/_generated/api";

const convexUrl = process.env.NEXT_PUBLIC_CONVEX_URL;
if (!convexUrl) {
  console.error("❌ NEXT_PUBLIC_CONVEX_URL is not defined!");
}

const convex = new ConvexHttpClient(convexUrl as string);

export async function POST(req: NextRequest) {
  try {
    const { role, email, password } = await req.json();
    if (!role || !email || !password) {
      return NextResponse.json({ error: "missing_fields" }, { status: 400 });
    }

    const result = await convex.action(api.auth.validateLogin, { role, email, password });
    if (!result.success) {
      return NextResponse.json({ error: result.error }, { status: 401 });
    }

    const cookiePayload = JSON.stringify(result.user);
    const res = NextResponse.json({ ok: true });
    res.cookies.set("session", cookiePayload, {
      httpOnly: true,
      sameSite: "lax",
      secure: process.env.NODE_ENV === "production",
      path: "/",
      maxAge: 60 * 60 * 24 * 7,
    });
    return res;
  } catch (e) {
    console.error("Login API error:", e);
    return NextResponse.json({ error: "server_error" }, { status: 500 });
  }
}


