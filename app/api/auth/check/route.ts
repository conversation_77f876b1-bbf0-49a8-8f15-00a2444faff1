import { NextRequest, NextResponse } from "next/server";

export async function GET(req: NextRequest) {
  try {
    // Get the session cookie
    const sessionCookie = req.cookies.get("session");
    
    if (!sessionCookie || !sessionCookie.value) {
      return NextResponse.json({ error: "Not authenticated" }, { status: 401 });
    }

    // Parse the session data
    let userData;
    try {
      userData = JSON.parse(sessionCookie.value);
    } catch (error) {
      return NextResponse.json({ error: "Invalid session" }, { status: 401 });
    }

    // Return the user data
    return NextResponse.json({ 
      authenticated: true, 
      user: userData 
    });
    
  } catch (error) {
    console.error("Auth check error:", error);
    return NextResponse.json({ error: "Server error" }, { status: 500 });
  }
}
