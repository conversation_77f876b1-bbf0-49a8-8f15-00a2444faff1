"use client"

import { useLanguage } from "@/components/providers/language-provider"
import { Heart, Users, Award, Globe } from "lucide-react"
import Image from "next/image"

export default function AboutPage() {
  const { t } = useLanguage()

  const stats = [
    { icon: Users, label: "Registered Doctors", value: "500+" },
    { icon: Heart, label: "Happy Patients", value: "10,000+" },
    { icon: Award, label: "Years of Service", value: "5+" },
    { icon: Globe, label: "Cities Covered", value: "3" },
  ]

  const team = [
    {
      name: "Dr. <PERSON>",
      role: "Chief Medical Officer",
      image: "/doctors-placeholder.png",
      bio: "Leading healthcare innovation for the Sudanese community in Egypt.",
    },
    {
      name: "<PERSON>",
      role: "CEO & Founder",
      image: "/doctors-placeholder.png",
      bio: "Passionate about connecting patients with quality healthcare.",
    },
    {
      name: "Dr. <PERSON>",
      role: "Head of Quality Assurance",
      image: "/doctors-placeholder.png",
      bio: "Ensuring the highest standards of medical care and service.",
    },
  ]

  return (
    <div className="min-h-screen bg-white">
      {/* Hero Section */}
      <section className="bg-gradient-to-br from-blue-50 to-white py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center space-y-6">
            <h1 className="text-4xl md:text-6xl font-bold text-gray-900">About HealthCare Sudan</h1>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              We're dedicated to connecting Sudanese patients with trusted doctors in Egypt, making quality healthcare
              accessible and convenient for our community.
            </p>
          </div>
        </div>
      </section>

      {/* Mission Section */}
      <section className="py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <div className="space-y-6">
              <h2 className="text-3xl font-bold text-gray-900">Our Mission</h2>
              <p className="text-lg text-gray-700 leading-relaxed">
                HealthCare Sudan was founded with a simple yet powerful mission: to bridge the gap between Sudanese
                patients and quality healthcare in Egypt. We understand the unique challenges faced by our community
                when seeking medical care in a foreign country.
              </p>
              <p className="text-lg text-gray-700 leading-relaxed">
                Our platform connects you with verified Sudanese doctors who understand your language, culture, and
                medical needs. We make it easy to find, compare, and book appointments with trusted healthcare
                providers.
              </p>
            </div>
            <div className="relative">
              <Image
                src="/doctors-placeholder.png"
                alt="Healthcare Mission"
                width={600}
                height={400}
                className="rounded-lg shadow-lg"
              />
            </div>
          </div>
        </div>
      </section>

      {/* Stats Section */}
      <section className="py-16 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900">Our Impact</h2>
            <p className="text-xl text-gray-600 mt-4">Making a difference in healthcare accessibility</p>
          </div>

          <div className="grid grid-cols-2 md:grid-cols-4 gap-8">
            {stats.map((stat, index) => {
              const Icon = stat.icon
              return (
                <div key={index} className="text-center">
                  <div className="mx-auto w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mb-4">
                    <Icon className="h-8 w-8 text-blue-600" />
                  </div>
                  <div className="text-3xl font-bold text-gray-900 mb-2">{stat.value}</div>
                  <div className="text-gray-600">{stat.label}</div>
                </div>
              )
            })}
          </div>
        </div>
      </section>

      {/* Team Section */}
      <section className="py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900">Meet Our Team</h2>
            <p className="text-xl text-gray-600 mt-4">Dedicated professionals working to improve healthcare access</p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {team.map((member, index) => (
              <div key={index} className="text-center">
                <div className="relative mb-6">
                  <Image
                    src={member.image || "/doctors-placeholder.png"}
                    alt={member.name}
                    width={200}
                    height={200}
                    className="rounded-full mx-auto object-cover"
                  />
                </div>
                <h3 className="text-xl font-semibold text-gray-900 mb-2">{member.name}</h3>
                <p className="text-blue-600 font-medium mb-4">{member.role}</p>
                <p className="text-gray-600">{member.bio}</p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Values Section */}
      <section className="py-16 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900">Our Values</h2>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <div className="text-center space-y-4">
              <div className="mx-auto w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center">
                <Heart className="h-8 w-8 text-blue-600" />
              </div>
              <h3 className="text-xl font-semibold text-gray-900">Compassionate Care</h3>
              <p className="text-gray-600">We believe healthcare should be delivered with empathy and understanding.</p>
            </div>

            <div className="text-center space-y-4">
              <div className="mx-auto w-16 h-16 bg-green-100 rounded-full flex items-center justify-center">
                <Award className="h-8 w-8 text-green-600" />
              </div>
              <h3 className="text-xl font-semibold text-gray-900">Quality Excellence</h3>
              <p className="text-gray-600">We maintain the highest standards in healthcare services and technology.</p>
            </div>

            <div className="text-center space-y-4">
              <div className="mx-auto w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center">
                <Users className="h-8 w-8 text-purple-600" />
              </div>
              <h3 className="text-xl font-semibold text-gray-900">Community Focus</h3>
              <p className="text-gray-600">We're committed to serving the unique needs of the Sudanese community.</p>
            </div>
          </div>
        </div>
      </section>
    </div>
  )
}
