"use client"

import { useState, useMemo } from "react"
import { useQuery } from "convex/react"
import { api } from "@/convex/_generated/api"
import { Doctor<PERSON><PERSON> } from "@/components/ui/doctor-card"
import { MobileFilters } from "@/components/ui/mobile-filters"
import { useLanguage } from "@/components/providers/language-provider"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Grid, List, Search, SlidersHorizontal } from "lucide-react"
import { <PERSON> } from "@/types"
import { getDoctorFullName, getDoctorClinic } from "@/lib/utils"

export default function DoctorsPage() {
  const { t } = useLanguage()
  const [viewMode, setViewMode] = useState<"grid" | "list">("grid")
  const [sortBy, setSortBy] = useState("best-match")
  const [searchQuery, setSearchQuery] = useState("")
  const [showMobileFilters, setShowMobileFilters] = useState(false)

  // Fetch doctors from Convex
  const doctors = useQuery(api.doctors.getAllDoctors)

  // Filter and sort doctors based on search and sort criteria
  const filteredAndSortedDoctors = useMemo(() => {
    if (!doctors) return []
    
    let filtered = doctors.filter(doctor => {
      if (!searchQuery) return true
      const query = searchQuery.toLowerCase()
      const doctorName = getDoctorFullName(doctor).toLowerCase()
      const doctorClinic = getDoctorClinic(doctor).toLowerCase()
      
      return (
        doctorName.includes(query) ||
        doctor.specialty.toLowerCase().includes(query) ||
        doctorClinic.includes(query)
      )
    })

    // Apply sorting
    switch (sortBy) {
      case "highest-rated":
        // Note: Rating not available in current schema, so we'll sort by name
        filtered.sort((a, b) => getDoctorFullName(a).localeCompare(getDoctorFullName(b)))
        break
      case "lowest-price":
        // Note: Price not available in current schema, so we'll sort by name
        filtered.sort((a, b) => getDoctorFullName(a).localeCompare(getDoctorFullName(b)))
        break
      case "availability":
        // Note: Availability not available in current schema, so we'll sort by name
        filtered.sort((a, b) => getDoctorFullName(a).localeCompare(getDoctorFullName(b)))
        break
      default: // "best-match"
        filtered.sort((a, b) => getDoctorFullName(a).localeCompare(getDoctorFullName(b)))
        break
    }

    return filtered
  }, [doctors, searchQuery, sortBy])

  // Loading state
  if (doctors === undefined) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-gray-50 to-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="text-center">
            <div className="flex flex-col items-center space-y-4">
              <img
                src="/Doctor-Symbol-Preloader.gif"
                alt="Loading..."
                className="w-20 h-20"
              />
              <div className="text-gray-600 text-sm font-medium">
                Loading Doctors...
              </div>
            </div>
          </div>
        </div>
      </div>
    )
  }

  // Empty state
  if (doctors.length === 0) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-gray-50 to-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="text-center">
            <div className="inline-flex items-center px-4 py-2 bg-yellow-50 rounded-full text-yellow-700 text-sm font-medium">
              No doctors available at the moment
            </div>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 to-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="text-center mb-12">
          <h1 className="text-4xl font-bold text-gray-900 mb-4">
            {t("doctors.title")}
          </h1>
          <p className="text-xl text-gray-600 max-w-2xl mx-auto">
            {t("doctors.subtitle")}
          </p>
        </div>

        {/* Search and Filters */}
        <div className="bg-white rounded-2xl shadow-lg border border-gray-100 p-6 mb-8">
          <div className="flex flex-col lg:flex-row gap-4">
            {/* Search Bar */}
            <div className="flex-1 relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-5 w-5" />
              <Input
                placeholder={t("doctors.search_placeholder")}
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-10 h-12 text-lg border-2 focus:border-blue-500 focus:ring-blue-500"
              />
            </div>

            {/* Sort Dropdown */}
            <Select value={sortBy} onValueChange={setSortBy}>
              <SelectTrigger className="w-full lg:w-48 h-12 text-lg border-2">
                <SelectValue placeholder={t("doctors.sort_by")} />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="best-match">{t("doctors.sort_best_match")}</SelectItem>
                <SelectItem value="highest-rated">{t("doctors.sort_highest_rated")}</SelectItem>
                <SelectItem value="lowest-price">{t("doctors.sort_lowest_price")}</SelectItem>
                <SelectItem value="availability">{t("doctors.sort_availability")}</SelectItem>
              </SelectContent>
            </Select>

            {/* View Mode Toggle */}
            <div className="flex border-2 rounded-lg overflow-hidden">
              <Button
                variant={viewMode === "grid" ? "default" : "ghost"}
                size="sm"
                onClick={() => setViewMode("grid")}
                className="rounded-none border-0 h-12 px-4"
              >
                <Grid className="h-5 w-5" />
              </Button>
              <Button
                variant={viewMode === "list" ? "default" : "ghost"}
                size="sm"
                onClick={() => setViewMode("list")}
                className="rounded-none border-0 h-12 px-4"
              >
                <List className="h-5 w-5" />
              </Button>
            </div>

            {/* Mobile Filters Button */}
            <Button
              variant="outline"
              size="sm"
              onClick={() => setShowMobileFilters(true)}
              className="lg:hidden h-12 px-4 border-2"
            >
              <SlidersHorizontal className="h-5 w-5 mr-2" />
              {t("common.filters")}
            </Button>
          </div>
        </div>

        {/* Results Count */}
        <div className="mb-6">
          <p className="text-gray-600">
            {t("doctors.results_count", { count: filteredAndSortedDoctors.length })}
          </p>
        </div>

        {/* Doctors Grid/List */}
        <div className={viewMode === "grid" ? "grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6" : "space-y-4"}>
          {filteredAndSortedDoctors.map((doctor) => (
            <DoctorCard key={doctor._id} doctor={doctor} />
          ))}
        </div>

        {/* Mobile Filters Modal */}
        <MobileFilters
          isOpen={showMobileFilters}
          onClose={() => setShowMobileFilters(false)}
        />
      </div>
    </div>
  )
}
