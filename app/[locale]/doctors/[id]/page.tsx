"use client"
import { <PERSON><PERSON> } from "@/components/ui/badge"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/tabs"
import { BookingWidget } from "@/components/ui/booking-widget"
import { useLanguage } from "@/components/providers/language-provider"
import { useQuery } from "convex/react"
import { api } from "@/convex/_generated/api"
import { Star, MapPin, Clock, GraduationCap, Languages, Award } from "lucide-react"
import Image from "next/image"
import { useParams } from "next/navigation"
import { <PERSON> } from "@/types"
import { transformDoctorToDisplay, getDoctorFullName, getDoctorTitle, getDoctorClinic, getDoctorPhone } from "@/lib/utils"

interface DoctorProfilePageProps {
  params: {
    id: string
  }
}

export default function DoctorProfilePage({ params }: DoctorProfilePageProps) {
  const { t } = useLanguage()
  const { id } = useParams()
  
  // Fetch doctor data from Convex
  // Note: We need to handle the case where the ID might not be a valid Convex ID
  const doctor = useQuery(api.doctors.getDoctorById, { 
    id: id as any // Type assertion needed since URL params are strings
  })

  // Loading state
  if (doctor === undefined) {
    return (
      <div className="min-h-screen bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="text-center">
            <div className="flex flex-col items-center space-y-4">
              <img
                src="/Doctor-Symbol-Preloader.gif"
                alt="Loading..."
                className="w-20 h-20"
              />
              <div className="text-gray-600 text-sm font-medium">
                Loading Doctor Profile...
              </div>
            </div>
          </div>
        </div>
      </div>
    )
  }

  // Doctor not found
  if (!doctor) {
    return (
      <div className="min-h-screen bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="text-center">
            <div className="inline-flex items-center px-4 py-2 bg-red-50 rounded-full text-red-700 text-sm font-medium">
              Doctor not found
            </div>
          </div>
        </div>
      </div>
    )
  }

  // Transform backend data to display format
  const displayDoctor = transformDoctorToDisplay(doctor)

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Hero Section */}
      <div className="bg-white shadow-sm">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="flex flex-col md:flex-row gap-6">
            <div className="flex-shrink-0">
              <Image
                src={doctor.image || "/doctors-placeholder.png"}
                alt={displayDoctor.displayName}
                width={200}
                height={200}
                className="rounded-xl object-cover"
              />
            </div>

            <div className="flex-1 space-y-4">
              <div>
                <h1 className="text-3xl font-bold text-gray-900">{displayDoctor.displayName}</h1>
                <p className="text-xl text-gray-600">{doctor.specialty}</p>
                {displayDoctor.displayTitle && (
                  <p className="text-lg text-blue-600">{displayDoctor.displayTitle}</p>
                )}
              </div>

              <div className="flex items-center space-x-4 rtl:space-x-reverse">
                {displayDoctor.displayClinic && (
                  <div className="flex items-center text-gray-600">
                    <MapPin className="h-5 w-5 mr-1 rtl:ml-1 rtl:mr-0" />
                    {displayDoctor.displayClinic}
                  </div>
                )}
                {displayDoctor.displayPhone && (
                  <div className="flex items-center text-gray-600">
                    <Clock className="h-5 w-5 mr-1 rtl:ml-1 rtl:mr-0" />
                    {displayDoctor.displayPhone}
                  </div>
                )}
              </div>

              <div className="flex flex-wrap gap-2">
                <Badge variant="secondary" className="bg-blue-100 text-blue-800">
                  {doctor.specialty}
                </Badge>
                {doctor.practiceType && (
                  <Badge variant="secondary" className="bg-green-100 text-green-800">
                    {doctor.practiceType === 'clinic-hospital' ? 'Clinic/Hospital' : 'Telemedicine'}
                  </Badge>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Left Column - Doctor Info */}
          <div className="lg:col-span-2 space-y-8">
            {/* About Section */}
            <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
              <h2 className="text-2xl font-bold text-gray-900 mb-4">About</h2>
              <div className="space-y-4">
                {doctor.notes && (
                  <p className="text-gray-600 leading-relaxed">{doctor.notes}</p>
                )}
                {!doctor.notes && (
                  <p className="text-gray-500 italic">No additional information available.</p>
                )}
              </div>
            </div>

            {/* Working Hours Section */}
            <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
              <h2 className="text-2xl font-bold text-gray-900 mb-4 flex items-center">
                <Clock className="h-6 w-6 mr-2" />
                Working Hours
              </h2>
              <div className="space-y-2">
                {displayDoctor.displayWorkingHours ? (
                  <p className="text-gray-600">{displayDoctor.displayWorkingHours}</p>
                ) : (
                  <p className="text-gray-500 italic">Working hours not specified.</p>
                )}
              </div>
            </div>

            {/* Contact Information */}
            <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
              <h2 className="text-2xl font-bold text-gray-900 mb-4">Contact Information</h2>
              <div className="space-y-3">
                {doctor.email && (
                  <div className="flex items-center text-gray-600">
                    <span className="font-medium w-24">Email:</span>
                    <span>{doctor.email}</span>
                  </div>
                )}
                {displayDoctor.displayPhone && (
                  <div className="flex items-center text-gray-600">
                    <span className="font-medium w-24">Phone:</span>
                    <span>{displayDoctor.displayPhone}</span>
                  </div>
                )}
                {displayDoctor.displayClinic && (
                  <div className="flex items-center text-gray-600">
                    <span className="font-medium w-24">Location:</span>
                    <span>{displayDoctor.displayClinic}</span>
                  </div>
                )}
              </div>
            </div>
          </div>

          {/* Right Column - Booking Widget */}
          <div className="lg:col-span-1">
            <div className="sticky top-8">
              <BookingWidget doctor={doctor} />
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
