"use client"

import { ClinicCard } from "@/components/ui/clinic-card"
import { useLanguage } from "@/components/providers/language-provider"
import { useQuery } from "convex/react"
import { api } from "@/convex/_generated/api"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Input } from "@/components/ui/input"
import { Search } from "lucide-react"
import { useState, useMemo } from "react"

export default function ClinicsPage() {
  const { t } = useLanguage()
  const [searchTerm, setSearchTerm] = useState("")
  const [selectedSpecialty, setSelectedSpecialty] = useState("")
  const [selectedCity, setSelectedCity] = useState("")
  
  // Get real data from Convex
  const doctors = useQuery(api.doctors.getAllDoctors) || []
  
  // Group doctors by clinic and transform to clinic format
  const clinics = useMemo(() => {
    const clinicMap = new Map()
    
    doctors.forEach(doctor => {
      const clinicName = doctor.clinicName || doctor.clinic_name || "Independent Practice"
      const location = doctor.address || "Location not specified"
      
      if (!clinicMap.has(clinicName)) {
        clinicMap.set(clinicName, {
          id: clinicName,
          name: clinicName,
          specialties: new Set(),
          rating: 4.5, // Default rating - you can add this to your schema later
          reviewCount: Math.floor(Math.random() * 50) + 10, // Placeholder - add to schema later
          location: location,
          image: doctor.image || "/doctors-placeholder.png",
          doctorCount: 0
        })
      }
      
      const clinic = clinicMap.get(clinicName)
      clinic.specialties.add(doctor.specialty)
      clinic.doctorCount++
    })
    
    return Array.from(clinicMap.values()).map(clinic => ({
      ...clinic,
      specialties: Array.from(clinic.specialties)
    }))
  }, [doctors])
  
  // Filter clinics based on search and filters
  const filteredClinics = useMemo(() => {
    return clinics.filter(clinic => {
      const matchesSearch = clinic.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                           clinic.location.toLowerCase().includes(searchTerm.toLowerCase())
      const matchesSpecialty = !selectedSpecialty || clinic.specialties.includes(selectedSpecialty)
      const matchesCity = !selectedCity || clinic.location.toLowerCase().includes(selectedCity.toLowerCase())
      
      return matchesSearch && matchesSpecialty && matchesCity
    })
  }, [clinics, searchTerm, selectedSpecialty, selectedCity])
  
  // Get unique specialties and cities for filters
  const allSpecialties = useMemo(() => {
    const specialties = new Set<string>()
    doctors.forEach(doctor => specialties.add(doctor.specialty))
    return Array.from(specialties).sort()
  }, [doctors])
  
  const allCities = useMemo(() => {
    const cities = new Set<string>()
    doctors.forEach(doctor => {
      if (doctor.address) {
        const city = doctor.address.split(',')[0]?.trim()
        if (city) cities.add(city)
      }
    })
    return Array.from(cities).sort()
  }, [doctors])

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">Medical Clinics</h1>
          <p className="text-gray-600">Find the best medical centers and clinics</p>
        </div>

        {/* Search and Filters */}
        <div className="bg-white rounded-lg shadow-sm border p-6 mb-8">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div className="relative">
              <Search className="absolute left-3 rtl:right-3 rtl:left-auto top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
              <Input 
                placeholder="Search clinics..." 
                className="pl-10 rtl:pr-10 rtl:pl-3"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
            </div>

            <Select value={selectedSpecialty} onValueChange={setSelectedSpecialty}>
              <SelectTrigger>
                <SelectValue placeholder="Specialty" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="">All Specialties</SelectItem>
                {allSpecialties.map((specialty) => (
                  <SelectItem key={specialty} value={specialty}>{specialty}</SelectItem>
                ))}
              </SelectContent>
            </Select>

            <Select value={selectedCity} onValueChange={setSelectedCity}>
              <SelectTrigger>
                <SelectValue placeholder="City" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="">All Cities</SelectItem>
                {allCities.map((city) => (
                  <SelectItem key={city} value={city}>{city}</SelectItem>
                ))}
              </SelectContent>
            </Select>

            <Select>
              <SelectTrigger>
                <SelectValue placeholder="Sort by" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="rating">Highest Rated</SelectItem>
                <SelectItem value="name">Name A-Z</SelectItem>
                <SelectItem value="location">Location</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>

        {/* Results */}
        <div className="mb-6">
          <p className="text-gray-600">Showing {filteredClinics.length} clinics</p>
        </div>

        {filteredClinics.length === 0 ? (
          <div className="text-center py-12">
            <p className="text-gray-500 text-lg">No clinics found matching your criteria.</p>
          </div>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {filteredClinics.map((clinic) => (
              <ClinicCard key={clinic.id} clinic={clinic} />
            ))}
          </div>
        )}
      </div>
    </div>
  )
}
