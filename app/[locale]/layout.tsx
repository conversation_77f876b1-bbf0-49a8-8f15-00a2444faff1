import type React from "react"
import type { <PERSON>ada<PERSON> } from "next"
import { Inter, Cairo } from "next/font/google"
import { LanguageProvider } from "@/components/providers/language-provider"
import { ConvexProviderWrapper } from "@/components/providers/convex-provider"
import { Navbar } from "@/components/layout/navbar"
import { Footer } from "@/components/layout/footer"

const inter = Inter({ subsets: ["latin"], variable: "--font-inter" })
const cairo = Cairo({ subsets: ["arabic"], variable: "--font-cairo" })

export const metadata: Metadata = {
  title: "HealthCare Sudan - Book Doctors Online",
  description:
    "Find and book appointments with the best Sudanese doctors in Egypt. Read reviews, compare prices, and book instantly.",
}

interface LocaleLayoutProps {
  children: React.ReactNode
  params: {
    locale: string
  }
}

export default function LocaleLayout({
  children,
  params,
}: LocaleLayoutProps) {
  const { locale } = params

  return (
    <html lang={locale} dir={locale === 'ar' ? 'rtl' : 'ltr'} suppressHydrationWarning>
      <body className={`${inter.variable} ${cairo.variable} font-sans antialiased`}>
        <ConvexProviderWrapper>
          <LanguageProvider initialLocale={locale as 'en' | 'ar'}>
            <div className="min-h-screen flex flex-col">
              <Navbar />
              <main className="flex-1">{children}</main>
              <Footer />
            </div>
          </LanguageProvider>
        </ConvexProviderWrapper>
      </body>
    </html>
  )
}
