"use client"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { <PERSON><PERSON>, <PERSON><PERSON>Content, <PERSON>bsList, TabsTrigger } from "@/components/ui/tabs"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { useLanguage } from "@/components/providers/language-provider"
import { Stethoscope, User, UserCheck, ChevronLeft, ChevronRight, Check } from "lucide-react"
import Link from "next/link"
import { useState } from "react"
import { useAction, useMutation } from "convex/react"
import { api } from "@/convex/_generated/api"
import { useParams, useRouter } from "next/navigation"

export default function SignupPage() {
  const { t } = useLanguage()
  const params = useParams() as { locale?: string }
  const locale = params?.locale || "en"
  const router = useRouter()

  const registerPatient = useAction(api.auth.registerPatient)
  const registerDoctor = useAction(api.auth.registerDoctor)
  const runDoctorMigration = useMutation(api.migrations.updateDoctorFieldNames)


  const [submitting, setSubmitting] = useState(false)
  const [errorMessage, setErrorMessage] = useState<string | null>(null)
  const [successMessage, setSuccessMessage] = useState<string | null>(null)
  const [patientStep, setPatientStep] = useState(1)
  const [doctorStep, setDoctorStep] = useState(1)
  const [emailError, setEmailError] = useState<string | null>(null)
  const [emailValid, setEmailValid] = useState<boolean>(false)

  // Patient form state
  const [patientForm, setPatientForm] = useState({
    firstName: '',
    lastName: '',
    email: '',
    phone: '',
    password: ''
  })

  // Doctor form state
  const [doctorForm, setDoctorForm] = useState({
    firstName: '',
    lastName: '',
    email: '',
    phones: [{ raw: '', label: 'primary' }],
    professionalTitle: '',
    specialty: '',
    practiceType: '',
    clinic_name: '',
    address: '',
    workingHours: {} as Record<string, { enabled: boolean; start: string; end: string }>,
    notes: '',
    password: ''
  })

  const updatePatientForm = (field: string, value: string) => {
    setPatientForm(prev => ({ ...prev, [field]: value }))
    
    // Clear email error when user types in email field
    if (field === 'email') {
      setEmailError(null)
    }
  }

  const updateDoctorForm = (field: string, value: string | Record<string, { enabled: boolean; start: string; end: string }> | Array<{ raw: string; label: string }>) => {
    setDoctorForm(prev => ({ ...prev, [field]: value }))
    
    // Clear email error when user types in email field
    if (field === 'email') {
      setEmailError(null)
    }
  }

  const nextStep = (type: 'patient' | 'doctor') => {
    if (type === 'patient') {
      // Validate email before allowing to proceed to next step
      if (patientStep === 1 && patientForm.email && !validateEmail(patientForm.email)) {
        setEmailError("Please enter a valid email address before proceeding.")
        return
      }
      // Clear email error when proceeding to next step
      if (patientStep === 1) {
        setEmailError(null)
      }
      setPatientStep(Math.min(patientStep + 1, 3))
    } else {
      // Validate email before allowing to proceed to next step
      if (doctorStep === 1 && doctorForm.email && !validateEmail(doctorForm.email)) {
        setEmailError("Please enter a valid email address before proceeding.")
        return
      }
      // Clear email error when proceeding to next step
      if (doctorStep === 1) {
        setEmailError(null)
      }
      setDoctorStep(Math.min(doctorStep + 1, 5))
    }
  }

  const prevStep = (type: 'patient' | 'doctor') => {
    if (type === 'patient') {
      setPatientStep(Math.max(patientStep - 1, 1))
      // Clear email error when going back to step 1
      if (patientStep === 2) {
        setEmailError(null)
        setEmailValid(false)
      }
    } else {
      setDoctorStep(Math.max(doctorStep - 1, 1))
      // Clear email error when going back to step 1
      if (doctorStep === 2) {
        setEmailError(null)
        setEmailValid(false)
      }
    }
  }

  const resetSteps = (type: 'patient' | 'doctor') => {
    if (type === 'patient') {
      setPatientStep(1)
      setPatientForm({ firstName: '', lastName: '', email: '', phone: '', password: '' })
    } else {
      setDoctorStep(1)
      setDoctorForm({ firstName: '', lastName: '', email: '', phones: [{ raw: '', label: 'primary' }], professionalTitle: '', specialty: '', practiceType: '', clinic_name: '', address: '', workingHours: {} as Record<string, { enabled: boolean; start: string; end: string }>, notes: '', password: '' })
    }
    setEmailError(null)
    setEmailValid(false)
  }

  // Email validation function
  const validateEmail = (email: string): boolean => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    return emailRegex.test(email)
  }

  // Real-time email validation
  const handleEmailChange = (email: string, type: 'patient' | 'doctor') => {
    if (type === 'patient') {
      updatePatientForm('email', email)
    } else {
      updateDoctorForm('email', email)
    }
    
    // Clear email error when user starts typing
    if (emailError) {
      setEmailError(null)
    }
    
    // Show validation error for invalid email format (only if user has typed something)
    if (email && !validateEmail(email)) {
      setEmailError("Please enter a valid email address.")
      setEmailValid(false)
    } else if (email && validateEmail(email)) {
      setEmailValid(true)
    } else {
      setEmailValid(false)
    }
  }

  async function handleCreatePatient() {
    setErrorMessage(null)
    setSuccessMessage(null)
    setEmailError(null)
    
    if (!patientForm.firstName || !patientForm.lastName || !patientForm.email || !patientForm.password || !patientForm.phone) {
      setErrorMessage("Please fill in all required patient fields.")
      return
    }
    
    if (!patientForm.email.trim()) {
      setEmailError("Email address is required.")
      return
    }
    
    if (!validateEmail(patientForm.email)) {
      setEmailError("Please enter a valid email address.")
      return
    }
    
    if (emailError) {
      return
    }
    
    // Final email validation check
    if (!validateEmail(patientForm.email)) {
      setEmailError("Please enter a valid email address.")
      return
    }
    
    try {
      setSubmitting(true)
      await registerPatient({
        firstName: patientForm.firstName,
        lastName: patientForm.lastName,
        email: patientForm.email,
        phone: patientForm.phone,
        password: patientForm.password,
        userType: "patient"
      })
      setSuccessMessage("Patient account created. Redirecting to login…")
      setTimeout(() => router.push(`/${locale}/auth/login`), 800)
    } catch (err: any) {
      // Handle specific email uniqueness errors
      if (err?.message?.includes("already exists")) {
        if (err.message.includes("doctor account")) {
          setErrorMessage("This email is already registered as a doctor. Please use a different email or contact support.")
        } else if (err.message.includes("Patient")) {
          setErrorMessage("This email is already registered. Please sign in instead or use a different email.")
        } else {
          setErrorMessage("This email is already registered. Please use a different email.")
        }
      } else {
        setErrorMessage(err?.message || "Failed to create patient account")
      }
    } finally {
      setSubmitting(false)
    }
  }

  async function handleCreateDoctor() {
    setErrorMessage(null)
    setSuccessMessage(null)
    setEmailError(null)
    
    if (!doctorForm.firstName || !doctorForm.lastName || !doctorForm.email || !doctorForm.password || !doctorForm.specialty || !doctorForm.practiceType) {
      setErrorMessage("Please fill in all required doctor fields.")
      return
    }
    
    if (!doctorForm.email.trim()) {
      setEmailError("Email address is required.")
      return
    }
    
    if (!validateEmail(doctorForm.email)) {
      setEmailError("Please enter a valid email address.")
      return
    }
    
    if (emailError) {
      return
    }
    
    // Final email validation check
    if (!validateEmail(doctorForm.email)) {
      setEmailError("Please enter a valid email address.")
      return
    }
    
    try {
      setSubmitting(true)
      await registerDoctor({
        firstName: doctorForm.firstName,
        lastName: doctorForm.lastName,
        email: doctorForm.email,
        password: doctorForm.password,
        professionalTitle: doctorForm.professionalTitle || "doctor",
        specialty: doctorForm.specialty,
        practiceType: doctorForm.practiceType as "clinic-hospital" | "telemedicine",
        clinic_name: doctorForm.clinic_name || undefined,
        address: doctorForm.address || undefined,
        workingHours: Object.keys(doctorForm.workingHours || {}).length ? doctorForm.workingHours : undefined,
        phones: doctorForm.phones || undefined,
        notes: doctorForm.notes || undefined,
      })
      setSuccessMessage("Doctor account created. Redirecting to login…")
      setTimeout(() => router.push(`/${locale}/auth/login`), 800)
    } catch (err: any) {
      // Handle specific email uniqueness errors
      if (err?.message?.includes("already exists")) {
        if (err.message.includes("patient account")) {
          setErrorMessage("This email is already registered as a patient. Please use a different email or contact support.")
        } else if (err.message.includes("Doctor")) {
          setErrorMessage("This email is already registered. Please sign in instead or use a different email.")
        } else {
          setErrorMessage("This email is already registered. Please use a different email.")
        }
      } else {
        setErrorMessage(err?.message || "Failed to create doctor account")
      }
    } finally {
      setSubmitting(false)
    }
  }

  return (
    <div className="min-h-screen bg-white flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-md w-full space-y-8">
        <div className="text-center">
          <div className="flex justify-center">
            <Stethoscope className="h-12 w-12 text-blue-600" />
          </div>
          <h2 className="mt-6 text-3xl font-bold text-gray-900 dark:text-white">Create your account</h2>
          <p className="mt-2 text-sm text-gray-700 dark:text-gray-300">
            Already have an account?{" "}
            <Link href={`/${locale}/auth/login`} className="font-medium text-blue-600 hover:text-blue-500 dark:text-blue-400 dark:hover:text-blue-300 transition-colors">
              Sign in
            </Link>
          </p>
          {/* Temporary migration button - remove after use */}
          <button 
            onClick={() => runDoctorMigration()}
            className="mt-4 px-4 py-2 bg-orange-500 text-white rounded hover:bg-orange-600"
          >
            Fix Doctor Fields (Remove after use)
          </button>

        </div>

        <div className="bg-white dark:bg-gray-800 rounded-xl shadow-lg border border-gray-200 dark:border-gray-700 p-8">
          {(errorMessage || successMessage) && (
            <div className={`mb-4 text-sm ${errorMessage ? "text-red-600" : "text-green-600"}`}>
              {errorMessage || successMessage}
            </div>
          )}
          <Tabs defaultValue="patient" className="space-y-6">
            <TabsList className="grid w-full grid-cols-2 bg-gray-100 dark:bg-gray-700">
              <TabsTrigger 
                value="patient" 
                className="flex items-center space-x-2 rtl:space-x-reverse text-gray-700 dark:text-gray-300 data-[state=active]:bg-white dark:data-[state=active]:bg-gray-600 data-[state=active]:text-blue-600 dark:data-[state=active]:text-blue-400 transition-all"
                onClick={() => {
                  resetSteps('patient')
                  setEmailError(null)
                  setEmailValid(false)
                }}
              >
                <User className="h-4 w-4" />
                <span>Patient</span>
              </TabsTrigger>
              <TabsTrigger 
                value="doctor" 
                className="flex items-center space-x-2 rtl:space-x-reverse text-gray-700 dark:text-gray-300 data-[state=active]:bg-white dark:data-[state=active]:bg-gray-600 data-[state=active]:text-blue-600 dark:data-[state=active]:text-blue-400 transition-all"
                onClick={() => {
                  resetSteps('doctor')
                  setEmailError(null)
                  setEmailValid(false)
                }}
              >
                <UserCheck className="h-4 w-4" />
                <span>Doctor</span>
              </TabsTrigger>
            </TabsList>

            <TabsContent value="patient" className="space-y-6">
              {/* Patient Step Indicator */}
              <div className="flex items-center justify-center mb-6">
                <div className="flex items-center space-x-1">
                  {/* Step 1: Basic Info */}
                  <div className={`w-6 h-6 rounded-full flex items-center justify-center text-xs font-medium ${
                    patientStep >= 1 ? 'bg-blue-600 text-white' : 'bg-gray-200 dark:bg-gray-600 text-gray-500 dark:text-gray-400'
                  }`}>
                    {patientStep > 1 ? <Check className="w-3 h-3" /> : '1'}
                  </div>
                  
                  {/* Connector */}
                  <div className={`w-4 h-0.5 ${
                    patientStep > 1 ? 'bg-blue-600' : 'bg-gray-200 dark:bg-gray-600'
                  }`} />
                  
                  {/* Step 2: Contact */}
                  <div className={`w-6 h-6 rounded-full flex items-center justify-center text-xs font-medium ${
                    patientStep >= 2 ? 'bg-blue-600 text-white' : 'bg-gray-200 dark:bg-gray-600 text-gray-500 dark:text-gray-400'
                  }`}>
                    {patientStep > 2 ? <Check className="w-3 h-3" /> : '2'}
                  </div>
                  
                  {/* Connector */}
                  <div className={`w-4 h-0.5 ${
                    patientStep > 2 ? 'bg-blue-600' : 'bg-gray-200 dark:bg-gray-600'
                  }`} />
                  
                  {/* Step 3: Review */}
                  <div className={`w-6 h-6 rounded-full flex items-center justify-center text-xs font-medium ${
                    patientStep >= 3 ? 'bg-blue-600 text-white' : 'bg-gray-200 dark:bg-gray-600 text-gray-500 dark:text-gray-400'
                  }`}>
                    {patientStep > 3 ? <Check className="w-3 h-3" /> : '✓'}
                  </div>
                </div>
              </div>

              {/* Patient Step 1: Basic Info */}
              {patientStep === 1 && (
                <div className="space-y-4">
                  <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">Step 1: Basic Information</h3>
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor="first-name" className="text-gray-800 dark:text-gray-200 font-medium cursor-pointer">First Name</Label>
                      <Input 
                        id="first-name" 
                        placeholder="First name" 
                        value={patientForm.firstName}
                        onChange={(e) => updatePatientForm('firstName', e.target.value)}
                        className="mt-1 border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder:text-gray-500 dark:placeholder:text-gray-400 focus:border-blue-500 focus:ring-blue-500" 
                      />
                    </div>
                    <div>
                      <Label htmlFor="last-name" className="text-gray-800 dark:text-gray-200 font-medium cursor-pointer">Last Name</Label>
                      <Input 
                        id="last-name" 
                        placeholder="Last name" 
                        value={patientForm.lastName}
                        onChange={(e) => updatePatientForm('lastName', e.target.value)}
                        className="mt-1 border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder:text-gray-500 dark:placeholder:text-gray-400 focus:border-blue-500 focus:ring-blue-500" 
                      />
                    </div>
                  </div>
                  <div>
                    <Label htmlFor="email" className="text-gray-800 dark:text-gray-200 font-medium cursor-pointer">Email address</Label>
                    <Input 
                      id="email" 
                      type="email" 
                      placeholder="Enter your email" 
                      value={patientForm.email}
                      onChange={(e) => handleEmailChange(e.target.value, 'patient')}
                      className="mt-1 border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder:text-gray-500 dark:placeholder:text-gray-400 focus:border-blue-500 focus:ring-blue-500" 
                    />
                    {emailError && (
                      <p className="mt-1 text-sm text-red-600">{emailError}</p>
                    )}
                    {emailValid && patientForm.email && (
                      <p className="mt-1 text-sm text-green-600 flex items-center">
                        <Check className="w-4 h-4 mr-1" />
                        Valid email address
                      </p>
                    )}
                  </div>
                  <div>
                    <Label htmlFor="phone" className="text-gray-800 dark:text-gray-200 font-medium cursor-pointer">Phone Number</Label>
                    <Input 
                      id="phone" 
                      type="tel" 
                      placeholder="Enter your phone" 
                      value={patientForm.phone}
                      onChange={(e) => updatePatientForm('phone', e.target.value)}
                      className="mt-1 border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder:text-gray-500 dark:placeholder:text-gray-400 focus:border-blue-500 focus:ring-blue-500" 
                    />
                  </div>
                </div>
              )}

              {/* Patient Step 2: Password */}
              {patientStep === 2 && (
                <div className="space-y-4">
                  <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">Step 2: Security</h3>
                  <div>
                    <Label htmlFor="password" className="text-gray-800 dark:text-gray-200 font-medium cursor-pointer">Password</Label>
                    <Input 
                      id="password" 
                      type="password" 
                      placeholder="Create a password" 
                      value={patientForm.password}
                      onChange={(e) => updatePatientForm('password', e.target.value)}
                      className="mt-1 border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder:text-gray-500 dark:placeholder:text-gray-400 focus:border-blue-500 focus:ring-blue-500" 
                    />
                  </div>
                </div>
              )}

              {/* Patient Step 3: Review */}
              {patientStep === 3 && (
                <div className="space-y-4">
                  <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">Step 3: Review & Create</h3>
                  <div className="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg space-y-3">
                    <div className="flex justify-between">
                      <span className="text-gray-600 dark:text-gray-400">Name:</span>
                      <span className="text-gray-900 dark:text-white font-medium">
                        {patientForm.firstName} {patientForm.lastName}
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600 dark:text-gray-400">Email:</span>
                      <span className="text-gray-900 dark:text-white font-medium">{patientForm.email}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600 dark:text-gray-400">Phone:</span>
                      <span className="text-gray-900 dark:text-white font-medium">{patientForm.phone}</span>
                    </div>
                  </div>
                  <div className="text-sm text-gray-600 dark:text-gray-400 text-center">
                    Please review your information above. You can go back to make changes.
                  </div>
                </div>
              )}

              {/* Patient Navigation */}
              <div className="flex justify-between pt-4">
                {patientStep > 1 && (
                  <Button 
                    onClick={() => prevStep('patient')}
                    className="flex items-center space-x-2 text-black bg-transparent hover:bg-gray-100 hover:text-gray-800 border-0 shadow-none"
                  >
                    <ChevronLeft className="w-4 h-4" />
                    <span>Previous</span>
                  </Button>
                )}
                {patientStep < 3 ? (
                  <Button 
                    onClick={() => nextStep('patient')}
                    className="flex items-center space-x-2 bg-blue-600 hover:bg-blue-700 text-white"
                  >
                    <span>Next</span>
                    <ChevronRight className="w-4 h-4" />
                  </Button>
                ) : (
                  <div className="w-full flex justify-end">
                    <Button onClick={handleCreatePatient} disabled={submitting} className="w-auto bg-blue-600 hover:bg-blue-700 text-white font-semibold py-3 px-6 rounded-lg transition-colors duration-200 shadow-md hover:shadow-lg">
                      {submitting ? "Creating…" : "Create Patient Account"}
                    </Button>
                  </div>
                )}
              </div>
            </TabsContent>

            <TabsContent value="doctor" className="space-y-6">
              {/* Doctor Step Indicator */}
              <div className="flex items-center justify-center mb-6">
                <div className="flex items-center space-x-1">
                  {/* Step 1: Basic Info */}
                  <div className={`w-6 h-6 rounded-full flex items-center justify-center text-xs font-medium ${
                    doctorStep >= 1 ? 'bg-blue-600 text-white' : 'bg-gray-200 dark:bg-gray-600 text-gray-500 dark:text-gray-400'
                  }`}>
                    {doctorStep > 1 ? <Check className="w-3 h-3" /> : '1'}
                  </div>
                  
                  {/* Connector */}
                  <div className={`w-4 h-0.5 ${
                    doctorStep > 1 ? 'bg-blue-600' : 'bg-gray-200 dark:bg-gray-600'
                  }`} />
                  
                  {/* Step 2: Professional */}
                  <div className={`w-6 h-6 rounded-full flex items-center justify-center text-xs font-medium ${
                    doctorStep >= 2 ? 'bg-blue-600 text-white' : 'bg-gray-200 dark:bg-gray-600 text-gray-500 dark:text-gray-400'
                  }`}>
                    {doctorStep > 2 ? <Check className="w-3 h-3" /> : '2'}
                  </div>
                  
                  {/* Connector */}
                  <div className={`w-4 h-0.5 ${
                    doctorStep > 2 ? 'bg-blue-600' : 'bg-gray-200 dark:bg-gray-600'
                  }`} />
                  
                  {/* Step 3: Working Hours */}
                  <div className={`w-6 h-6 rounded-full flex items-center justify-center text-xs font-medium ${
                    doctorStep >= 3 ? 'bg-blue-600 text-white' : 'bg-gray-200 dark:bg-gray-600 text-gray-500 dark:text-gray-400'
                  }`}>
                    {doctorStep > 3 ? <Check className="w-3 h-3" /> : '3'}
                  </div>
                  
                  {/* Connector */}
                  <div className={`w-4 h-0.5 ${
                    doctorStep > 3 ? 'bg-blue-600' : 'bg-gray-200 dark:bg-gray-600'
                  }`} />
                  
                  {/* Step 4: Final Details */}
                  <div className={`w-6 h-6 rounded-full flex items-center justify-center text-xs font-medium ${
                    doctorStep >= 4 ? 'bg-blue-600 text-white' : 'bg-gray-200 dark:bg-gray-600 text-gray-500 dark:text-gray-400'
                  }`}>
                    {doctorStep > 4 ? <Check className="w-3 h-3" /> : '4'}
                  </div>
                  
                  {/* Connector */}
                  <div className={`w-4 h-0.5 ${
                    doctorStep > 4 ? 'bg-blue-600' : 'bg-gray-200 dark:bg-gray-600'
                  }`} />
                  
                  {/* Step 5: Review */}
                  <div className={`w-6 h-6 rounded-full flex items-center justify-center text-xs font-medium ${
                    doctorStep >= 5 ? 'bg-blue-600 text-white' : 'bg-gray-200 dark:bg-gray-600 text-gray-500 dark:text-gray-400'
                  }`}>
                    {doctorStep > 5 ? <Check className="w-3 h-3" /> : '✓'}
                  </div>
                </div>
              </div>

              {/* Doctor Step 1: Basic Info */}
              {doctorStep === 1 && (
                <div className="space-y-4">
                  <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">Step 1: Basic Information</h3>
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor="doctor-first-name" className="text-gray-800 dark:text-gray-200 font-medium cursor-pointer">First Name</Label>
                      <Input 
                        id="doctor-first-name" 
                        placeholder="First name" 
                        value={doctorForm.firstName}
                        onChange={(e) => updateDoctorForm('firstName', e.target.value)}
                        className="mt-1 border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder:text-gray-500 dark:placeholder:text-gray-400 focus:border-blue-500 focus:ring-blue-500" 
                      />
                    </div>
                    <div>
                      <Label htmlFor="doctor-last-name" className="text-gray-800 dark:text-gray-200 font-medium cursor-pointer">Last Name</Label>
                      <Input 
                        id="doctor-last-name" 
                        placeholder="Last name" 
                        value={doctorForm.lastName}
                        onChange={(e) => updateDoctorForm('lastName', e.target.value)}
                        className="mt-1 border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder:text-gray-500 dark:placeholder:text-gray-400 focus:border-blue-500 focus:ring-blue-500" 
                      />
                    </div>
                  </div>
                  <div>
                    <Label htmlFor="doctor-email" className="text-gray-800 dark:text-gray-200 font-medium cursor-pointer">Email address</Label>
                    <Input 
                      id="doctor-email" 
                      type="email" 
                      placeholder="Enter your email" 
                      value={doctorForm.email}
                      onChange={(e) => handleEmailChange(e.target.value, 'doctor')}
                      className="mt-1 border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder:text-gray-500 dark:placeholder:text-gray-400 focus:border-blue-500 focus:ring-blue-500" 
                    />
                    {emailError && (
                      <p className="mt-1 text-sm text-red-600">{emailError}</p>
                    )}
                    {emailValid && doctorForm.email && (
                      <p className="mt-1 text-sm text-green-600 flex items-center">
                        <Check className="w-4 h-4 mr-1" />
                        Valid email address
                      </p>
                    )}
                  </div>
                  <div>
                                        <Label htmlFor="phone" className="text-gray-800 dark:text-gray-200 font-medium cursor-pointer">Phone Number</Label>
                    <Input 
                      id="phone" 
                      type="tel" 
                      placeholder="Enter your phone number" 
                      value={doctorForm.phones[0]?.raw || ''}
                      onChange={(e) => updateDoctorForm('phones', [{ raw: e.target.value, label: 'primary' }])}
                      className="mt-1 border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder:text-gray-500 dark:placeholder:text-gray-400 focus:border-blue-500 focus:ring-blue-500" 
                    />
                  </div>
                </div>
              )}

              {/* Doctor Step 2: Professional Info */}
              {doctorStep === 2 && (
                <div className="space-y-4">
                  <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">Step 2: Professional Information</h3>
                  <div>
                    <Label htmlFor="doctor-title" className="text-gray-800 dark:text-gray-200 font-medium cursor-pointer">Professional Title</Label>
                    <Select value={doctorForm.professionalTitle} onValueChange={(value) => updateDoctorForm('professionalTitle', value)}>
                      <SelectTrigger className="mt-1 border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:border-blue-500 focus:ring-blue-500">
                        <SelectValue placeholder="Select your title" className="text-gray-500 dark:text-gray-400" />
                      </SelectTrigger>
                      <SelectContent className="bg-white dark:bg-gray-700 border-gray-300 dark:border-gray-600">
                        <SelectItem value="doctor" className="text-gray-900 dark:text-white hover:bg-blue-50 dark:hover:bg-blue-900/20 focus:bg-blue-50 dark:focus:bg-blue-900/20 focus:text-gray-900 dark:focus:text-white cursor-pointer">د. / Dr.</SelectItem>
                        <SelectItem value="consultant" className="text-gray-900 dark:text-white hover:bg-blue-50 dark:hover:bg-blue-900/20 focus:bg-blue-50 dark:focus:bg-blue-900/20 focus:text-gray-900 dark:focus:text-white cursor-pointer">استشاري / Consultant</SelectItem>
                        <SelectItem value="professor" className="text-gray-900 dark:text-white hover:bg-blue-50 dark:hover:bg-blue-900/20 focus:bg-blue-50 dark:focus:bg-blue-900/20 focus:text-gray-900 dark:focus:text-white cursor-pointer">أستاذ / Professor</SelectItem>
                        <SelectItem value="specialist" className="text-gray-900 dark:text-white hover:bg-blue-50 dark:hover:bg-blue-900/20 focus:bg-blue-50 dark:focus:bg-blue-900/20 focus:text-gray-900 dark:focus:text-white cursor-pointer">أخصائي / Specialist</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div>
                    <Label htmlFor="specialty" className="text-gray-800 dark:text-gray-200 font-medium cursor-pointer">Specialty</Label>
                    <Select value={doctorForm.specialty} onValueChange={(value) => updateDoctorForm('specialty', value)}>
                      <SelectTrigger className="mt-1 border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:border-blue-500 focus:ring-blue-500">
                        <SelectValue placeholder="Select your specialty" className="text-gray-500 dark:placeholder:text-gray-400" />
                      </SelectTrigger>
                      <SelectContent className="bg-white dark:bg-gray-700 border-gray-300 dark:border-gray-600">
                        <SelectItem value="internal-medicine" className="text-gray-900 dark:text-white hover:bg-blue-50 dark:hover:bg-blue-900/20 focus:bg-blue-50 dark:focus:bg-blue-900/20 focus:text-gray-900 dark:focus:text-white cursor-pointer">Internal Medicine / باطنة عامة</SelectItem>
                        <SelectItem value="cardiology" className="text-gray-900 dark:text-white hover:bg-blue-50 dark:hover:bg-blue-900/20 focus:bg-blue-50 dark:focus:bg-blue-900/20 focus:text-gray-900 dark:focus:text-white cursor-pointer">Cardiology / أمراض القلب</SelectItem>
                        <SelectItem value="pediatrics" className="text-gray-900 dark:text-white hover:bg-blue-50 dark:hover:bg-blue-900/20 focus:bg-blue-50 dark:focus:bg-blue-900/20 focus:text-gray-900 dark:focus:text-white cursor-pointer">Pediatrics / طب الأطفال</SelectItem>
                        <SelectItem value="dentistry" className="text-gray-900 dark:text-white hover:bg-blue-50 dark:hover:bg-blue-900/20 focus:bg-blue-50 dark:focus:bg-blue-50 dark:focus:bg-blue-900/20 focus:text-gray-900 dark:focus:text-white cursor-pointer">Dentistry / طب الأسنان</SelectItem>
                        <SelectItem value="dermatology" className="text-gray-900 dark:text-white hover:bg-blue-50 dark:hover:bg-blue-900/20 focus:bg-blue-50 dark:focus:bg-blue-900/20 focus:text-gray-900 dark:focus:text-white cursor-pointer">Dermatology / الجلدية</SelectItem>
                        <SelectItem value="neurology" className="text-gray-900 dark:text-white hover:bg-blue-50 dark:hover:bg-blue-900/20 focus:bg-blue-50 dark:focus:bg-blue-900/20 focus:text-gray-900 dark:focus:text-white cursor-pointer">Neurology / مخ وأعصاب</SelectItem>
                        <SelectItem value="surgery" className="text-gray-900 dark:text-white hover:bg-blue-50 dark:hover:bg-blue-900/20 focus:bg-blue-50 dark:focus:bg-blue-50 dark:focus:bg-blue-900/20 focus:text-gray-900 dark:focus:text-white cursor-pointer">General Surgery / الجراحة العامة</SelectItem>
                        <SelectItem value="gynecology" className="text-gray-900 dark:text-white hover:bg-blue-50 dark:hover:bg-blue-900/20 focus:bg-blue-50 dark:focus:bg-blue-900/20 focus:text-gray-900 dark:focus:text-white cursor-pointer">Gynecology / أمراض نساء وولادة</SelectItem>
                        <SelectItem value="oncology" className="text-gray-900 dark:text-white hover:bg-blue-50 dark:hover:bg-blue-900/20 focus:bg-blue-50 dark:focus:bg-blue-900/20 focus:text-gray-900 dark:focus:text-white cursor-pointer">Oncology / الأورام</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div>
                    <Label htmlFor="practice-type" className="text-gray-800 dark:text-gray-200 font-medium cursor-pointer">Practice Type</Label>
                    <Select value={doctorForm.practiceType} onValueChange={(value) => updateDoctorForm('practiceType', value)}>
                      <SelectTrigger className="mt-1 border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:border-blue-500 focus:ring-blue-500">
                        <SelectValue placeholder="Select your practice type" className="text-gray-500 dark:text-gray-400" />
                      </SelectTrigger>
                      <SelectContent className="bg-white dark:bg-gray-700 border-gray-300 dark:border-gray-600">
                        <SelectItem value="clinic-hospital" className="text-gray-900 dark:text-white hover:bg-blue-50 dark:hover:bg-blue-900/20 focus:bg-blue-50 dark:focus:bg-blue-900/20 focus:text-gray-900 dark:focus:text-white cursor-pointer">Clinic/Hospital</SelectItem>
                        <SelectItem value="telemedicine" className="text-gray-900 dark:text-white hover:bg-blue-50 dark:hover:bg-blue-900/20 focus:bg-blue-50 dark:focus:bg-blue-900/20 focus:text-gray-900 dark:focus:text-white cursor-pointer">Calls & Messages Only</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  
                  {/* Conditional Clinic Details */}
                  {doctorForm.practiceType === 'clinic-hospital' && (
                    <>
                      <div>
                        <Label htmlFor="clinic-name" className="text-gray-800 dark:text-gray-200 font-medium cursor-pointer">Clinic/Hospital Name</Label>
                        <Input 
                          id="clinic-name" 
                          placeholder="Enter clinic or hospital name" 
                          value={doctorForm.clinic_name}
                          onChange={(e) => updateDoctorForm('clinic_name', e.target.value)}
                          className="mt-1 border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder:text-gray-500 dark:placeholder:text-gray-400 focus:border-blue-500 focus:ring-blue-500" 
                        />
                      </div>
                      <div>
                        <Label htmlFor="address" className="text-gray-800 dark:text-gray-200 font-medium cursor-pointer">Address</Label>
                        <Input 
                          id="address" 
                          placeholder="Enter your clinic address" 
                          value={doctorForm.address}
                          onChange={(e) => updateDoctorForm('address', e.target.value)}
                          className="mt-1 border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder:text-gray-500 dark:placeholder:text-gray-400 focus:border-blue-500 focus:ring-blue-500" 
                        />
                      </div>
                    </>
                  )}
                </div>
              )}

              {/* Doctor Step 3: Working Hours */}
              {doctorStep === 3 && (
                <div className="space-y-4">
                  <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">Step 3: Working Hours</h3>
                  <div>
                    <Label className="text-gray-800 dark:text-gray-200 font-medium mb-3 block">Working Hours</Label>
                    <div className="grid grid-cols-1 gap-3">
                      {['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'].map((day, index) => (
                        <div key={day} className="border border-gray-200 dark:border-gray-600 rounded-lg overflow-hidden">
                          <div className="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-800">
                            <div className="flex items-center space-x-2">
                              <input
                                type="checkbox"
                                id={`day-${index}`}
                                checked={doctorForm.workingHours[day]?.enabled || false}
                                onChange={(e) => {
                                  const updatedHours = { ...doctorForm.workingHours }
                                  if (e.target.checked) {
                                    updatedHours[day] = { enabled: true, start: '09:00', end: '17:00' }
                                  } else {
                                    updatedHours[day] = { enabled: false, start: '', end: '' }
                                  }
                                  updateDoctorForm('workingHours', updatedHours)
                                }}
                                className="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600 cursor-pointer"
                              />
                              <Label htmlFor={`day-${index}`} className="text-sm font-medium text-gray-700 dark:text-gray-300 cursor-pointer">
                                {day}
                              </Label>
                            </div>
                            {doctorForm.workingHours[day]?.enabled && (
                              <span className="text-xs text-gray-500 dark:text-gray-400 bg-white dark:bg-gray-700 px-2 py-1 rounded">
                                {doctorForm.workingHours[day]?.start} - {doctorForm.workingHours[day]?.end}
                              </span>
                            )}
                          </div>
                          
                          {doctorForm.workingHours[day]?.enabled && (
                            <div className="p-3 bg-white dark:bg-gray-700 border-t border-gray-200 dark:border-gray-600">
                              <div className="flex items-center justify-center space-x-4">
                                <div className="text-center">
                                  <div className="text-xs text-gray-500 dark:text-gray-400 mb-1">Start Time</div>
                                  <input
                                    type="time"
                                    value={doctorForm.workingHours[day]?.start || '09:00'}
                                    onChange={(e) => {
                                      const updatedHours = { ...doctorForm.workingHours }
                                      updatedHours[day] = { ...updatedHours[day], start: e.target.value }
                                      updateDoctorForm('workingHours', updatedHours)
                                    }}
                                    className="text-sm bg-gray-50 dark:bg-gray-600 border border-gray-200 dark:border-gray-500 rounded-lg px-3 py-2 text-gray-900 dark:text-white focus:border-blue-500 focus:ring-2 focus:ring-blue-500 focus:outline-none cursor-pointer"
                                  />
                                </div>
                                
                                <div className="flex items-center">
                                  <div className="w-8 h-0.5 bg-gray-300 dark:bg-gray-500"></div>
                                  <div className="w-2 h-2 bg-blue-500 rounded-full mx-1"></div>
                                  <div className="w-8 h-0.5 bg-gray-300 dark:bg-gray-500"></div>
                                </div>
                                
                                <div className="text-center">
                                  <div className="text-xs text-gray-500 dark:text-gray-400 mb-1">End Time</div>
                                  <input
                                    type="time"
                                    value={doctorForm.workingHours[day]?.end || '17:00'}
                                    onChange={(e) => {
                                      const updatedHours = { ...doctorForm.workingHours }
                                      updatedHours[day] = { ...updatedHours[day], end: e.target.value }
                                      updateDoctorForm('workingHours', updatedHours)
                                    }}
                                    className="text-sm bg-gray-50 dark:bg-gray-600 border border-gray-200 dark:border-gray-500 rounded-lg px-3 py-2 text-gray-900 dark:text-white focus:border-blue-500 focus:ring-2 focus:ring-blue-500 focus:outline-none cursor-pointer"
                                  />
                                </div>
                              </div>
                            </div>
                          )}
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              )}

              

              {/* Doctor Step 4: Final Details */}
              {doctorStep === 4 && (
                <div className="space-y-4">
                  <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">Step 4: Final Details</h3>
                  <div>
                    <Label htmlFor="notes" className="text-gray-800 dark:text-gray-200 font-medium cursor-pointer">Additional Information</Label>
                    <textarea 
                      id="notes" 
                      placeholder="Brief description of your expertise, specializations, or any additional information" 
                      value={doctorForm.notes}
                      onChange={(e) => updateDoctorForm('notes', e.target.value)}
                      className="mt-1 w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder:text-gray-500 dark:placeholder:text-gray-400 focus:border-blue-500 focus:ring-blue-500 focus:outline-none resize-none cursor-pointer"
                      rows={3}
                    />
                  </div>
                  <div>
                    <Label htmlFor="password" className="text-gray-800 dark:text-gray-200 font-medium cursor-pointer">Password</Label>
                    <Input 
                      id="doctor-password" 
                      type="password" 
                      placeholder="Create a password" 
                      value={doctorForm.password}
                      onChange={(e) => updateDoctorForm('password', e.target.value)}
                      className="mt-1 border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder:text-gray-500 dark:placeholder:text-gray-400 focus:border-blue-500 focus:ring-blue-500" 
                    />
                  </div>
                </div>
              )}

              {/* Doctor Step 5: Review Information */}
              {doctorStep === 5 && (
                <div className="space-y-4">
                  <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">Step 5: Review Information</h3>
                  <div className="space-y-3 text-sm">
                    <div className="flex justify-between">
                      <span className="text-gray-600 dark:text-gray-400">Name:</span>
                      <span className="text-gray-900 dark:text-white font-medium">{doctorForm.firstName} {doctorForm.lastName}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600 dark:text-gray-400">Email:</span>
                      <span className="text-gray-900 dark:text-white font-medium">{doctorForm.email}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600 dark:text-gray-400">Phone:</span>
                      <span className="text-gray-900 dark:text-white font-medium">{doctorForm.phones[0]?.raw || 'Not specified'}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600 dark:text-gray-400">Title:</span>
                      <span className="text-gray-900 dark:text-white font-medium">{doctorForm.professionalTitle}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600 dark:text-gray-400">Specialty:</span>
                      <span className="text-gray-900 dark:text-white font-medium">{doctorForm.specialty}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600 dark:text-gray-400">Practice Type:</span>
                      <span className="text-gray-900 dark:text-white font-medium">{doctorForm.practiceType === 'clinic-hospital' ? 'Clinic/Hospital' : 'Calls & Messages Only'}</span>
                    </div>
                    {doctorForm.practiceType === 'clinic-hospital' && (
                      <>
                        <div className="flex justify-between">
                          <span className="text-gray-600 dark:text-gray-400">Clinic Name:</span>
                          <span className="text-gray-900 dark:text-white font-medium">{doctorForm.clinic_name || 'Not specified'}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-gray-600 dark:text-gray-400">Address:</span>
                          <span className="text-gray-900 dark:text-white font-medium">{doctorForm.address || 'Not specified'}</span>
                        </div>
                      </>
                    )}
                    <div className="flex justify-between">
                      <span className="text-gray-600 dark:text-gray-400">Working Hours:</span>
                      <span className="text-gray-900 dark:text-white font-medium">
                        {Object.keys(doctorForm.workingHours).filter(day => doctorForm.workingHours[day]?.enabled).length > 0 
                          ? Object.entries(doctorForm.workingHours)
                              .filter(([day, hours]) => hours?.enabled)
                              .map(([day, hours]) => `${day}: ${hours.start}-${hours.end}`)
                              .join(', ')
                          : 'Not specified'
                        }
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600 dark:text-gray-400">Additional Info:</span>
                      <span className="text-gray-900 dark:text-white font-medium">{doctorForm.notes || 'Not specified'}</span>
                    </div>
                  </div>
                </div>
              )}

              {/* Doctor Navigation */}
              <div className="flex justify-between pt-4">
                {doctorStep > 1 && (
                  <Button 
                    onClick={() => prevStep('doctor')}
                    className="flex items-center space-x-2 text-black bg-transparent hover:bg-gray-100 hover:text-gray-800 border-0 shadow-none"
                  >
                    <ChevronLeft className="w-4 h-4" />
                    <span>Previous</span>
                  </Button>
                )}
                {doctorStep < 5 ? (
                  <Button 
                    onClick={() => nextStep('doctor')}
                    className="flex items-center space-x-2 bg-blue-600 hover:bg-blue-700 text-white"
                  >
                    <span>Next</span>
                    <ChevronRight className="w-4 h-4" />
                  </Button>
                ) : (
                  <div className="w-full flex justify-end">
                    <Button onClick={handleCreateDoctor} disabled={submitting} className="w-auto bg-blue-600 hover:bg-blue-700 text-white font-semibold py-3 px-6 rounded-lg transition-colors duration-200 shadow-md hover:shadow-lg">
                      {submitting ? "Creating…" : "Create Doctor Account"}
                    </Button>
                  </div>
                )}
              </div>
            </TabsContent>
          </Tabs>
        </div>
      </div>
    </div>
  )
}
