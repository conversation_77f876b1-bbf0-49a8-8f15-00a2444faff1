"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Ta<PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { useLanguage } from "@/components/providers/language-provider"
import { Stethoscope, User, UserCheck } from "lucide-react"
import Link from "next/link"
import { useParams, useRouter } from "next/navigation"
// No direct Convex queries here; we use the Next.js auth API

export default function LoginPage() {
  const { t } = useLanguage()
  const [email, setEmail] = useState("")
  const [password, setPassword] = useState("")
  const [errorMessage, setErrorMessage] = useState<string | null>(null)
  const [successMessage, setSuccessMessage] = useState<string | null>(null)
  const [submitting, setSubmitting] = useState(false)
  const [isLoggedIn, setIsLoggedIn] = useState(false)
  const params = useParams() as { locale?: string }
  const locale = params?.locale || "en"
  const router = useRouter()

  // Queries are parameterized; we call them lazily via a small helper
  // Queries removed to avoid validation errors before email is provided

  async function signInAsPatient() {
    setErrorMessage(null)
    setSuccessMessage(null)
    if (!email || !password) {
      setErrorMessage("Please fill in all required fields.")
      return
    }
    try {
      setSubmitting(true)
      const res = await fetch(`/api/auth/login`, {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ role: "patient", email, password }),
      })
      if (!res.ok) {
        const data = await res.json().catch(() => ({}))
        setErrorMessage(data?.error || "Login failed")
        return
      }
      
      // Dispatch custom event to notify navbar about login
      window.dispatchEvent(new CustomEvent('user-login'));
      
      // Set logged in state to show loading GIF
      setIsLoggedIn(true)
      setSuccessMessage("Login successful. Redirecting to home…")
      
      setTimeout(() => router.push(`/${locale}`), 800)
    } catch (err: any) {
      setErrorMessage(err?.message || "Failed to login")
    } finally {
      setSubmitting(false)
    }
  }

  async function signInAsDoctor() {
    setErrorMessage(null)
    setSuccessMessage(null)
    if (!email || !password) {
      setErrorMessage("Please fill in all required fields.")
      return
    }
    try {
      setSubmitting(true)
      const res = await fetch(`/api/auth/login`, {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ role: "doctor", email, password }),
      })
      if (!res.ok) {
        const data = await res.json().catch(() => ({}))
        setErrorMessage(data?.error || "Login failed")
        return
      }
      
      // Dispatch custom event to notify navbar about login
      window.dispatchEvent(new CustomEvent('user-login'));
      
      // Set logged in state to show loading GIF
      setIsLoggedIn(true)
      setSuccessMessage("Login successful. Redirecting to home…")
      
      setTimeout(() => router.push(`/${locale}`), 800)
    } catch (err: any) {
      setErrorMessage(err?.message || "Failed to login")
    } finally {
      setSubmitting(false)
    }
  }

  // Show loading GIF if logged in
  if (isLoggedIn) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
        <div className="flex justify-center">
          <img 
            src="/Doctor-Symbol-Preloader.gif" 
            alt="Loading..." 
            className="object-contain"
          />
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50 flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-md w-full space-y-8">
        <div className="text-center">
          <div className="flex justify-center">
            <Stethoscope className="h-12 w-12 text-blue-600" />
          </div>
          <h2 className="mt-6 text-3xl font-bold text-gray-900">Sign in to your account</h2>
          <p className="mt-2 text-sm text-gray-600">
            Or{" "}
            <Link href={`/${locale}/auth/signup`} className="font-medium text-blue-600 hover:text-blue-500">
              create a new account
            </Link>
          </p>
        </div>

        <div className="bg-white rounded-lg shadow-sm border p-8">
          {errorMessage && (
            <div className="mb-4 text-sm text-red-600">{errorMessage}</div>
          )}
          {successMessage && (
            <div className="mb-4 text-sm text-green-600">{successMessage}</div>
          )}
          <Tabs defaultValue="patient" className="space-y-6">
            <TabsList className="grid w-full grid-cols-2">
              <TabsTrigger value="patient" className="flex items-center space-x-2 rtl:space-x-reverse">
                <User className="h-4 w-4" />
                <span>Patient</span>
              </TabsTrigger>
              <TabsTrigger value="doctor" className="flex items-center space-x-2 rtl:space-x-reverse">
                <UserCheck className="h-4 w-4" />
                <span>Doctor</span>
              </TabsTrigger>
            </TabsList>

            <TabsContent value="patient" className="space-y-6">
              <div className="space-y-4">
                <div>
                  <Label htmlFor="patient-email">Email address</Label>
                  <Input
                    id="patient-email"
                    type="email"
                    value={email}
                    onChange={(e) => setEmail(e.target.value)}
                    placeholder="Enter your email"
                    className="mt-1"
                  />
                </div>
                <div>
                  <Label htmlFor="patient-password">Password</Label>
                  <Input
                    id="patient-password"
                    type="password"
                    value={password}
                    onChange={(e) => setPassword(e.target.value)}
                    placeholder="Enter your password"
                    className="mt-1"
                  />
                </div>
              </div>

              <div className="flex items-center justify-between">
                <div className="text-sm">
                  <Link href="/auth/forgot-password" className="font-medium text-blue-600 hover:text-blue-500">
                    Forgot your password?
                  </Link>
                </div>
              </div>

              <Button onClick={signInAsPatient} disabled={submitting} className="w-full medical-primary">{submitting ? "Signing in…" : "Sign in as Patient"}</Button>
            </TabsContent>

            <TabsContent value="doctor" className="space-y-6">
              <div className="space-y-4">
                <div>
                  <Label htmlFor="doctor-email">Email address</Label>
                  <Input 
                    id="doctor-email" 
                    type="email" 
                    value={email}
                    onChange={(e) => setEmail(e.target.value)}
                    placeholder="Enter your email" 
                    className="mt-1" 
                  />
                </div>
                <div>
                  <Label htmlFor="doctor-password">Password</Label>
                  <Input 
                    id="doctor-password" 
                    type="password" 
                    value={password}
                    onChange={(e) => setPassword(e.target.value)}
                    placeholder="Enter your password" 
                    className="mt-1" 
                  />
                </div>
              </div>

              <div className="flex items-center justify-between">
                <div className="text-sm">
                  <Link href="/auth/forgot-password" className="font-medium text-blue-600 hover:text-blue-500">
                    Forgot your password?
                  </Link>
                </div>
              </div>

              <Button onClick={signInAsDoctor} disabled={submitting} className="w-full medical-primary">{submitting ? "Signing in…" : "Sign in as Doctor"}</Button>
            </TabsContent>
          </Tabs>
        </div>
      </div>
    </div>
  )
}
