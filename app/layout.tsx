import type { <PERSON>ada<PERSON> } from "next"
import { Inter } from "next/font/google"
import "./globals.css"
import { ConvexProviderWrapper } from "../components/providers/convex-provider"

const inter = Inter({ subsets: ["latin"] })

export const metadata: Metadata = {
  title: "Healthcare Booking Platform",
  description: "Find and book appointments with Sudanese doctors in Egypt",
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="en">
      <body className={inter.className}>
        <ConvexProviderWrapper>
          {children}
        </ConvexProviderWrapper>
      </body>
    </html>
  )
} 