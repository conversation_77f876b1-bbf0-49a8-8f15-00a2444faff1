/** @type {import('next').NextConfig} */
const nextConfig = {
  eslint: {
    ignoreDuringBuilds: true,
  },
  typescript: {
    ignoreBuildErrors: true,
  },
  images: {
    unoptimized: true,
  },
  // Handle static assets with internationalization
  assetPrefix: process.env.NODE_ENV === 'production' ? '' : '',
  basePath: '',
  webpack: (config, { isServer }) => {
    // Suppress warnings about SWC binaries
    config.infrastructureLogging = {
      level: 'error',
    }
    
    // Ignore specific SWC binary warnings
    config.ignoreWarnings = [
      /Managed item.*@next\/swc.*isn't a directory/,
      /Managed item.*@next\/swc.*doesn't contain a package.json/,
    ]
    
    return config
  },
}

export default nextConfig
