"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { ClinicCard } from "@/components/ui/clinic-card"
import { useLanguage } from "@/components/providers/language-provider"
import { useQuery } from "convex/react"
import { api } from "@/convex/_generated/api"
import { useMemo } from "react"
import Link from "next/link"

export function FeaturedClinics() {
  const { t } = useLanguage()
  
  // Get real data from Convex
  const doctors = useQuery(api.doctors.getAllDoctors) || []
  
  // Group doctors by clinic and transform to clinic format
  const featuredClinics = useMemo(() => {
    const clinicMap = new Map()
    
    doctors.forEach(doctor => {
      const clinicName = doctor.clinicName || doctor.clinic_name || "Independent Practice"
      const location = doctor.address || "Location not specified"
      
      if (!clinicMap.has(clinicName)) {
        clinicMap.set(clinicName, {
          id: clinicName,
          name: clinicName,
          specialties: new Set(),
          rating: 4.5, // Default rating - you can add this to your schema later
          reviewCount: Math.floor(Math.random() * 50) + 10, // Placeholder - add to schema later
          location: location,
          image: doctor.image || "/doctors-placeholder.png",
          doctorCount: 0
        })
      }
      
      const clinic = clinicMap.get(clinicName)
      clinic.specialties.add(doctor.specialty)
      clinic.doctorCount++
    })
    
    return Array.from(clinicMap.values())
      .map(clinic => ({
        ...clinic,
        specialties: Array.from(clinic.specialties)
      }))
      .slice(0, 6) // Show only first 6 clinics
  }, [doctors])

  return (
    <section className="py-16">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center space-y-4 mb-12">
          <h2 className="text-3xl md:text-4xl font-bold text-gray-900">Featured Clinics</h2>
          <p className="text-xl text-gray-600">Top-rated medical centers and clinics</p>
        </div>

        {featuredClinics.length === 0 ? (
          <div className="text-center py-12">
            <p className="text-gray-500 text-lg">No clinics available yet.</p>
          </div>
        ) : (
          <>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
              {featuredClinics.map((clinic) => (
                <ClinicCard key={clinic.id} clinic={clinic} />
              ))}
            </div>

            <div className="text-center">
              <Button variant="outline" size="lg" asChild>
                <Link href="/clinics">
                  {t("common.view_all")} Clinics
                </Link>
              </Button>
            </div>
          </>
        )}
      </div>
    </section>
  )
}
