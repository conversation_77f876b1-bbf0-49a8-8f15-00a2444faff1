"use client"

import { useState } from "react"
import { useLanguage } from "@/components/providers/language-provider"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Search, Phone, Calendar } from "lucide-react"
import { ContainerTextFlip } from "@/components/ui/container-text-flip"
import Threads from "@/src/blocks/Backgrounds/Threads/Threads"

// Data constants
const SPECIALTIES = [
  { value: "dentist", label: "specialty.dentist" },
  { value: "pediatrician", label: "specialty.pediatrician" },
  { value: "psychiatrist", label: "specialty.psychiatrist" },
  { value: "cardiologist", label: "specialty.cardiologist" },
  { value: "dermatologist", label: "specialty.dermatologist" },
  { value: "orthopedic", label: "specialty.orthopedic" },
  { value: "gynecologist", label: "specialty.gynecologist" },
  { value: "neurologist", label: "specialty.neurologist" },
  { value: "ophthalmologist", label: "specialty.ophthalmologist" },
]

const CITIES = [
  { value: "cairo", label: "city.cairo" },
  { value: "giza", label: "city.giza" },
  { value: "alexandria", label: "city.alexandria" },
]

const AREAS = [
  { value: "maadi", label: "area.maadi" },
  { value: "zamalek", label: "area.zamalek" },
  { value: "heliopolis", label: "area.heliopolis" },
  { value: "nasr-city", label: "area.nasr-city" },
  { value: "dokki", label: "area.dokki" },
  { value: "mohandessin", label: "area.mohandessin" },
  { value: "6th-october", label: "area.6th-october" },
  { value: "new-cairo", label: "area.new-cairo" },
]

interface SearchFormData {
  specialty: string
  city: string
  area: string
  searchQuery: string
  specialist: string
}

export function HeroSection() {
  const { t, isRTL } = useLanguage()
  const [isTelehealth, setIsTelehealth] = useState(false)
  const [formData, setFormData] = useState<SearchFormData>({
    specialty: "",
    city: "",
    area: "",
    searchQuery: "",
    specialist: "",
  })

  // Helper function to update form data
  const updateFormData = (field: keyof SearchFormData, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }))
  }

  // Helper function to reset form when switching modes
  const handleModeSwitch = (telehealth: boolean) => {
    setIsTelehealth(telehealth)
    setFormData({
      specialty: "",
      city: "",
      area: "",
      searchQuery: "",
      specialist: "",
    })
  }

  // RTL-aware dropdown props
  const getDropdownProps = () => ({
    className: isRTL ? "rtl" : "",
    side: "bottom" as const,
    align: (isRTL ? "end" : "start") as "start" | "end",
    sideOffset: 4,
    dir: isRTL ? "rtl" : "ltr",
  })

  return (
    <section className="relative min-h-[90vh] flex items-center justify-center">
      {/* Threads Background */}
      <div className="absolute inset-0">
        <Threads 
          color={[0.2, 0.4, 0.8]} 
          amplitude={1.2} 
          distance={0.3} 
          enableMouseInteraction={false}
        />
      </div>

      <div className="relative w-full px-2 py-20">
        {/* Hero Content */}
        <div className="text-center animate-fade-in-up">
          <div className="space-y-8 mb-16">
            <h1 className="text-5xl md:text-7xl font-bold text-gray-900 leading-tight">
              {t("hero.find_and_book")}{" "}
              <span className="inline-block w-80 text-center">
                <ContainerTextFlip 
                  words={[t("hero.doctors"), t("hero.specialists"), t("hero.experts")]}
                  interval={2000}
                  className="w-full"
                />
              </span>
            </h1>
            <p className="text-xl md:text-2xl text-gray-900 max-w-3xl mx-auto leading-relaxed">
              {t("hero.subtitle")}
            </p>
          </div>
        </div>

        {/* Search Card */}
        <div className="text-center animate-slide-in-right">
          <div className="max-w-7xl mx-auto">
            <div className="bg-white rounded-3xl shadow-2xl border border-gray-100 overflow-hidden">
              {/* Mode Toggle */}
              <div className="flex w-full bg-gray-50 border-b border-gray-200">
                <button
                  onClick={() => handleModeSwitch(false)}
                  className={`flex-1 flex items-center justify-center py-6 px-4 transition-all duration-300 relative ${
                    !isTelehealth 
                      ? "bg-white text-blue-600 shadow-sm" 
                      : "text-gray-500 hover:text-gray-700 hover:bg-gray-100"
                  }`}
                >
                  {!isTelehealth && (
                    <div className="absolute bottom-0 left-0 right-0 h-1 bg-blue-600"></div>
                  )}
                  <div className="flex flex-col items-center">
                    <div className="flex items-center space-x-3 mb-2">
                      <Calendar className={`h-7 w-7 ${!isTelehealth ? 'text-blue-600' : 'text-gray-400'}`} />
                      <span className={`font-semibold text-lg ${!isTelehealth ? 'text-blue-600' : 'text-gray-600'}`}>
                        {t("hero.book_doctor")}
                      </span>
                    </div>
                    <span className={`text-base ${!isTelehealth ? 'text-blue-500' : 'text-gray-400'}`}>
                      {t("hero.in_person_visits")}
                    </span>
                  </div>
                </button>
                <button
                  onClick={() => handleModeSwitch(true)}
                  className={`flex-1 flex items-center justify-center py-6 px-4 transition-all duration-300 relative ${
                    isTelehealth 
                      ? "bg-white text-blue-600 shadow-sm" 
                      : "text-gray-500 hover:text-gray-700 hover:bg-gray-100"
                  }`}
                >
                  {isTelehealth && (
                    <div className="absolute bottom-0 left-0 right-0 h-1 bg-blue-600"></div>
                  )}
                  <div className="flex flex-col items-center">
                    <div className="flex items-center space-x-3 mb-2">
                      <Phone className={`h-7 w-7 ${isTelehealth ? 'text-blue-600' : 'text-gray-400'}`} />
                      <span className={`font-semibold text-lg ${isTelehealth ? 'text-blue-600' : 'text-gray-600'}`}>
                        {t("hero.telehealth")}
                      </span>
                    </div>
                    <span className={`text-base ${isTelehealth ? 'text-blue-500' : 'text-gray-400'}`}>
                      {t("hero.call_consultations")}
                    </span>
                  </div>
                </button>
              </div>

              {/* Search Form */}
              <div className="p-10 relative overflow-visible">
                <div className="grid grid-cols-1 md:grid-cols-12 gap-5">
                  {!isTelehealth ? (
                    // Book Doctor Mode
                    <>
                      {/* Specialty */}
                      <div className="md:col-span-2">
                        <Select value={formData.specialty} onValueChange={(value) => updateFormData("specialty", value)}>
                          <SelectTrigger className="h-16 border-0 bg-gray-50 focus:bg-white focus:ring-2 focus:ring-blue-500/20 text-lg text-gray-700 placeholder:text-gray-400 rtl:flex-row-reverse">
                            <SelectValue placeholder={t("hero.specialty")} className="text-right rtl:text-right" />
                          </SelectTrigger>
                          <SelectContent {...getDropdownProps()}>
                            <div className="grid grid-cols-2 gap-1 p-1">
                              {SPECIALTIES.map((specialty) => (
                                <SelectItem 
                                  key={specialty.value} 
                                  value={specialty.value}
                                  className="text-lg py-3 text-gray-700 hover:bg-blue-50 hover:text-blue-700 focus:bg-blue-50 focus:text-blue-700 cursor-pointer"
                                >
                                  {t(specialty.label)}
                                </SelectItem>
                              ))}
                            </div>
                          </SelectContent>
                        </Select>
                      </div>

                      {/* City */}
                      <div className="md:col-span-2">
                        <Select value={formData.city} onValueChange={(value) => updateFormData("city", value)}>
                          <SelectTrigger className="h-16 border-0 bg-gray-50 focus:bg-white focus:ring-2 focus:ring-blue-500/20 text-lg text-gray-700 placeholder:text-gray-400 rtl:flex-row-reverse">
                            <SelectValue placeholder={t("hero.in_this_city")} className="text-right rtl:text-right" />
                          </SelectTrigger>
                          <SelectContent {...getDropdownProps()}>
                            {CITIES.map((city) => (
                              <SelectItem 
                                key={city.value} 
                                value={city.value}
                                className="text-lg py-3 text-gray-700 hover:bg-blue-50 hover:text-blue-700 focus:bg-blue-50 focus:text-blue-700 cursor-pointer"
                              >
                                {t(city.label)}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </div>

                      {/* Area */}
                      <div className="md:col-span-2">
                        <Select value={formData.area} onValueChange={(value) => updateFormData("area", value)}>
                          <SelectTrigger className="h-16 border-0 bg-gray-50 focus:bg-white focus:ring-2 focus:ring-blue-500/20 text-lg text-gray-700 placeholder:text-gray-400 rtl:flex-row-reverse">
                            <SelectValue placeholder={t("hero.in_this_area")} className="text-right rtl:text-right" />
                          </SelectTrigger>
                          <SelectContent {...getDropdownProps()}>
                            <div className="grid grid-cols-2 gap-1 p-1">
                              {AREAS.map((area) => (
                                <SelectItem 
                                  key={area.value} 
                                  value={area.value}
                                  className="text-lg py-3 text-gray-700 hover:bg-blue-50 hover:text-blue-700 focus:bg-blue-50 focus:text-blue-700 cursor-pointer"
                                >
                                  {t(area.label)}
                                </SelectItem>
                              ))}
                            </div>
                          </SelectContent>
                        </Select>
                      </div>

                      {/* Search Input */}
                      <div className="md:col-span-3">
                        <div className="relative">
                          <Search className="absolute left-4 rtl:right-4 rtl:left-auto top-1/2 transform -translate-y-1/2 text-gray-400 h-6 w-6" />
                          <Input
                            placeholder={t("hero.search_by_name")}
                            value={formData.searchQuery}
                            onChange={(e) => updateFormData("searchQuery", e.target.value)}
                            className="pl-14 rtl:pr-14 rtl:pl-4 h-16 text-lg border-0 bg-gray-50 focus:bg-white focus:ring-2 focus:ring-blue-500/20 text-gray-700 placeholder:text-gray-400"
                          />
                        </div>
                      </div>

                      {/* Search Button */}
                      <div className="md:col-span-3">
                        <Button className="w-full h-16 text-lg bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 shadow-lg hover:shadow-xl transition-all duration-200">
                          <Search className="h-6 w-6 mr-2 rtl:ml-2 rtl:mr-0" />
                          {t("hero.search")}
                        </Button>
                      </div>
                    </>
                  ) : (
                    // Telehealth Mode
                    <>
                      {/* Search Input */}
                      <div className="md:col-span-6">
                        <div className="relative">
                          <Search className="absolute left-4 rtl:right-4 rtl:left-auto top-1/2 transform -translate-y-1/2 text-gray-400 h-6 w-6" />
                          <Input
                            placeholder={t("hero.search_telehealth_specialists")}
                            value={formData.searchQuery}
                            onChange={(e) => updateFormData("searchQuery", e.target.value)}
                            className="pl-14 rtl:pr-14 rtl:pl-4 h-16 text-lg border-0 bg-gray-50 focus:bg-white focus:ring-2 focus:ring-blue-500/20 text-gray-700 placeholder:text-gray-400"
                          />
                        </div>
                      </div>

                      {/* Specialist */}
                      <div className="md:col-span-3">
                        <Select value={formData.specialist} onValueChange={(value) => updateFormData("specialist", value)}>
                          <SelectTrigger className="h-16 border-0 bg-gray-50 focus:bg-white focus:ring-2 focus:ring-blue-500/20 text-lg text-gray-700 placeholder:text-gray-400 rtl:flex-row-reverse">
                            <SelectValue placeholder={t("hero.select_specialist")} className="text-right rtl:text-right" />
                          </SelectTrigger>
                          <SelectContent {...getDropdownProps()}>
                            <div className="grid grid-cols-2 gap-1 p-1">
                              {SPECIALTIES.map((specialty) => (
                                <SelectItem 
                                  key={specialty.value} 
                                  value={specialty.value}
                                  className="text-lg py-3 text-gray-700 hover:bg-blue-50 hover:text-blue-700 focus:bg-blue-50 focus:text-blue-700 cursor-pointer"
                                >
                                  {t(specialty.label)}
                                </SelectItem>
                              ))}
                            </div>
                          </SelectContent>
                        </Select>
                      </div>

                      {/* Search Button */}
                      <div className="md:col-span-3">
                        <Button className="w-full h-16 text-lg bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 shadow-lg hover:shadow-xl transition-all duration-200">
                          <Search className="h-6 w-6 mr-2 rtl:ml-2 rtl:mr-0" />
                          {t("hero.find_telehealth")}
                        </Button>
                      </div>
                    </>
                  )}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}
