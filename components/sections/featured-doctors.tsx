"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON><PERSON><PERSON> } from "@/components/ui/doctor-card"
import { useLanguage } from "@/components/providers/language-provider"
import { ArrowRight, Stethoscope } from "lucide-react"
import { useQuery } from "convex/react"
import { api } from "@/convex/_generated/api"
import Link from "next/link"
import { useMemo } from "react"
import { useParams } from "next/navigation"
import { ErrorBoundary } from "@/components/ui/error-boundary"

export function FeaturedDoctors() {
  const { t } = useLanguage()
  const params = useParams()
  const locale = params.locale || 'en'
  const doctors = useQuery(api.doctors.getFeaturedDoctors)

  // Memoize the doctors to prevent unnecessary re-renders
  const featuredDoctors = useMemo(() => {
    return doctors || []
  }, [doctors])

  // Show loading state only when doctors is undefined (initial load)
  if (doctors === undefined) {
    return (
      <section className="py-24 bg-gradient-to-br from-gray-50 to-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <div className="flex flex-col items-center space-y-4">
              <img
                src="/Doctor-Symbol-Preloader.gif"
                alt="Loading..."
                className="w-20 h-20"
              />
              <div className="text-gray-600 text-sm font-medium">
                Loading Doctors...
              </div>
            </div>
          </div>
        </div>
      </section>
    )
  }

  // Handle empty doctors array
  if (featuredDoctors.length === 0) {
    return (
      <section className="py-24 bg-gradient-to-br from-gray-50 to-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <div className="inline-flex items-center px-4 py-2 bg-yellow-50 rounded-full text-yellow-700 text-sm font-medium">
              <Stethoscope className="h-4 w-4 mr-2 rtl:ml-2 rtl:mr-0" />
              No doctors available at the moment
            </div>
          </div>
        </div>
      </section>
    )
  }

  return (
    <ErrorBoundary>
      <section className="py-24 bg-gradient-to-br from-gray-50 to-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center space-y-6 mb-16 animate-fade-in-up">
            <div className="inline-flex items-center px-4 py-2 bg-blue-50 rounded-full text-blue-700 text-sm font-medium">
              <Stethoscope className="h-4 w-4 mr-2 rtl:ml-2 rtl:mr-0" />
              Top Rated Doctors
            </div>

            <h2 className="text-4xl md:text-5xl font-bold text-gray-900">
              Meet Our{" "}
              <span className="bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
                Featured Doctors
              </span>
            </h2>

            <p className="text-xl text-gray-600 max-w-2xl mx-auto">
              Connect with highly-rated Sudanese doctors who understand your needs and speak your language.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-12">
            {featuredDoctors.map((doctor, index) => (
              <div key={doctor._id} className="animate-fade-in-up" style={{ animationDelay: `${index * 0.1}s` }}>
                <DoctorCard doctor={doctor} />
              </div>
            ))}
          </div>

          <div className="text-center animate-fade-in-up">
            <Button
              size="lg"
              variant="outline"
              className="px-8 py-4 text-lg border-2 hover:bg-blue-50 hover:border-blue-200 transition-all duration-200 group bg-transparent"
              asChild
            >
              <Link href={`/${locale}/doctors`}>
                {t("common.view_all")} Doctors
                <ArrowRight className="h-5 w-5 ml-2 rtl:ml-2 rtl:mr-0 group-hover:translate-x-1 rtl:group-hover:-translate-x-1 transition-transform" />
              </Link>
            </Button>
          </div>
        </div>
      </section>
    </ErrorBoundary>
  )
}
