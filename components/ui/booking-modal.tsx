"use client"

import { useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Calendar } from "@/components/ui/calendar"
import { <PERSON><PERSON>, <PERSON>alogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog"
import { useLanguage } from "@/components/providers/language-provider"
import { Clock, CreditCard, ArrowRight, ArrowLeft, CheckCircle } from "lucide-react"
import Image from "next/image"
import { Doctor } from "@/types"
import { transformDoctorToDisplay } from "@/lib/utils"

interface BookingModalProps {
  doctor: Doctor
  isOpen: boolean
  onClose: () => void
}

export function BookingModal({ doctor, isOpen, onClose }: BookingModalProps) {
  const { t } = useLanguage()
  const [step, setStep] = useState(1)
  const [selectedDate, setSelectedDate] = useState<Date | undefined>(new Date())
  const [selectedTime, setSelectedTime] = useState<string>("")
  
  // Transform backend data to display format
  const displayDoctor = transformDoctorToDisplay(doctor)

  const availableTimes = [
    "09:00 AM",
    "09:30 AM",
    "10:00 AM",
    "10:30 AM",
    "11:00 AM",
    "11:30 AM",
    "02:00 PM",
    "02:30 PM",
    "03:00 PM",
    "03:30 PM",
    "04:00 PM",
    "04:30 PM",
  ]

  const handleNext = () => {
    if (step < 3) setStep(step + 1)
  }

  const handleBack = () => {
    if (step > 1) setStep(step - 1)
  }

  const handleBooking = () => {
    // Handle booking logic here
    setStep(4) // Success step
  }

  const resetModal = () => {
    setStep(1)
    setSelectedDate(new Date())
    setSelectedTime("")
    onClose()
  }

  return (
    <Dialog open={isOpen} onOpenChange={resetModal}>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto custom-scrollbar">
        <DialogHeader className="pb-6 border-b border-gray-100">
          <div className="flex items-center space-x-4 rtl:space-x-reverse">
            <Image
              src={doctor.image || "/doctors-placeholder.png"}
              alt={displayDoctor.displayName}
              width={60}
              height={60}
              className="rounded-full object-cover"
            />
            <div>
              <DialogTitle className="text-2xl font-bold text-gray-900">{displayDoctor.displayName}</DialogTitle>
              <p className="text-blue-600 font-medium">{doctor.specialty}</p>
            </div>
          </div>
        </DialogHeader>

        {/* Progress Steps */}
        <div className="flex items-center justify-center space-x-4 rtl:space-x-reverse py-6">
          {[1, 2, 3].map((stepNumber) => (
            <div key={stepNumber} className="flex items-center">
              <div
                className={`w-10 h-10 rounded-full flex items-center justify-center font-semibold transition-all duration-200 ${
                  step >= stepNumber ? "bg-blue-500 text-white" : "bg-gray-200 text-gray-500"
                }`}
              >
                {step > stepNumber ? <CheckCircle className="h-5 w-5" /> : stepNumber}
              </div>
              {stepNumber < 3 && (
                <div
                  className={`w-16 h-1 mx-2 transition-all duration-200 ${
                    step > stepNumber ? "bg-blue-500" : "bg-gray-200"
                  }`}
                />
              )}
            </div>
          ))}
        </div>

        {/* Step Content */}
        {step === 1 && (
          <div className="space-y-6">
            <div className="text-center">
              <h3 className="text-xl font-semibold text-gray-900 mb-2">Select Date & Time</h3>
              <p className="text-gray-600">Choose when you'd like to book your appointment</p>
            </div>

            <div className="space-y-4">
              <h4 className="font-medium text-gray-900">Select Date</h4>
              <Calendar
                mode="single"
                selected={selectedDate}
                onSelect={setSelectedDate}
                disabled={(date) => date < new Date()}
                className="rounded-md border"
              />
            </div>

            {selectedDate && (
              <div className="space-y-4">
                <h4 className="font-medium text-gray-900">Select Time</h4>
                <div className="grid grid-cols-3 gap-2">
                  {availableTimes.map((time) => (
                    <Button
                      key={time}
                      variant={selectedTime === time ? "default" : "outline"}
                      size="sm"
                      onClick={() => setSelectedTime(time)}
                    >
                      {time}
                    </Button>
                  ))}
                </div>
              </div>
            )}

            <div className="flex justify-end space-x-3 rtl:space-x-reverse">
              <Button onClick={handleNext} disabled={!selectedDate || !selectedTime}>
                Next
                <ArrowRight className="h-4 w-4 ml-2 rtl:mr-2 rtl:ml-0" />
              </Button>
            </div>
          </div>
        )}

        {step === 2 && (
          <div className="space-y-6">
            <div className="text-center">
              <h3 className="text-xl font-semibold text-gray-900 mb-2">Confirm Details</h3>
              <p className="text-gray-600">Review your booking information</p>
            </div>

            <div className="bg-gray-50 rounded-lg p-4 space-y-3">
              <div className="flex justify-between">
                <span className="text-gray-600">Doctor:</span>
                <span className="font-medium">{displayDoctor.displayName}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Specialty:</span>
                <span className="font-medium">{doctor.specialty}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Date:</span>
                <span className="font-medium">{selectedDate?.toLocaleDateString()}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Time:</span>
                <span className="font-medium">{selectedTime}</span>
              </div>
            </div>

            <div className="flex justify-between space-x-3 rtl:space-x-reverse">
              <Button variant="outline" onClick={handleBack}>
                <ArrowLeft className="h-4 w-4 mr-2 rtl:ml-2 rtl:mr-0" />
                Back
              </Button>
              <Button onClick={handleNext}>
                Confirm Booking
                <ArrowRight className="h-4 w-4 ml-2 rtl:mr-2 rtl:ml-0" />
              </Button>
            </div>
          </div>
        )}

        {step === 3 && (
          <div className="space-y-6">
            <div className="text-center">
              <h3 className="text-xl font-semibold text-gray-900 mb-2">Payment Information</h3>
              <p className="text-gray-600">You will pay at the clinic. No upfront payment required.</p>
            </div>

            <div className="bg-blue-50 rounded-lg p-4">
              <div className="flex items-center space-x-3 rtl:space-x-reverse">
                <CreditCard className="h-6 w-6 text-blue-600" />
                <div>
                  <h4 className="font-medium text-blue-900">Pay at Clinic</h4>
                  <p className="text-sm text-blue-700">No online payment required</p>
                </div>
              </div>
            </div>

            <div className="flex justify-between space-x-3 rtl:space-x-reverse">
              <Button variant="outline" onClick={handleBack}>
                <ArrowLeft className="h-4 w-4 mr-2 rtl:ml-2 rtl:mr-0" />
                Back
              </Button>
              <Button onClick={handleBooking}>
                Complete Booking
                <ArrowRight className="h-4 w-4 ml-2 rtl:mr-2 rtl:ml-0" />
              </Button>
            </div>
          </div>
        )}

        {step === 4 && (
          <div className="text-center space-y-6">
            <div className="flex justify-center">
              <div className="w-20 h-20 bg-green-100 rounded-full flex items-center justify-center">
                <CheckCircle className="h-12 w-12 text-green-600" />
              </div>
            </div>
            
            <div>
              <h3 className="text-2xl font-bold text-gray-900 mb-2">Booking Confirmed!</h3>
              <p className="text-gray-600">
                Your appointment with {displayDoctor.displayName} has been successfully booked.
              </p>
            </div>

            <div className="bg-gray-50 rounded-lg p-4 text-left rtl:text-right">
              <h4 className="font-medium text-gray-900 mb-2">Appointment Details:</h4>
              <div className="space-y-1 text-sm text-gray-600">
                <p><strong>Date:</strong> {selectedDate?.toLocaleDateString()}</p>
                <p><strong>Time:</strong> {selectedTime}</p>
                <p><strong>Location:</strong> {displayDoctor.displayClinic}</p>
              </div>
            </div>

            <Button onClick={resetModal} className="w-full">
              Done
            </Button>
          </div>
        )}
      </DialogContent>
    </Dialog>
  )
}
