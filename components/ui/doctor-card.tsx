"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { useLanguage } from "@/components/providers/language-provider"
import { Star, MapPin, Clock, Calendar, ArrowRight } from "lucide-react"
import Image from "next/image"
import Link from "next/link"
import { useParams } from "next/navigation"
import { Doctor } from "@/types"
import { transformDoctorToDisplay } from "@/lib/utils"

interface DoctorCardProps {
  doctor: Doctor
}

export function DoctorCard({ doctor }: DoctorCardProps) {
  const { t, isRTL } = useLanguage()
  const params = useParams()
  const locale = params.locale || 'en'
  
  // Transform backend data to display format
  const displayDoctor = transformDoctorToDisplay(doctor)

  return (
    <div className="group bg-white rounded-3xl shadow-lg border border-gray-100 overflow-hidden hover-lift hover:shadow-2xl transition-all duration-300 h-full flex flex-col">
      <div className="relative flex flex-col h-full">
        {/* Doctor Image */}
        <div className="relative h-48 bg-gradient-to-br from-blue-50 to-purple-50 flex-shrink-0">
          <Image
            src={doctor.image || "/doctors-placeholder.png"}
            alt={displayDoctor.displayName}
            fill
            className="object-cover group-hover:scale-105 transition-transform duration-300"
            onError={(e) => {
              // Fallback to placeholder if image fails to load
              const target = e.target as HTMLImageElement;
              target.src = "/doctors-placeholder.png";
            }}
          />

          {/* Title Badge */}
          <div className={`absolute top-4 ${isRTL ? 'left-4' : 'right-4'}`}>
            <Badge className="bg-blue-500 text-white border-0 shadow-lg">
              {displayDoctor.displayTitle}
            </Badge>
          </div>
        </div>

        {/* Card Content */}
        <div className={`p-6 space-y-4 flex-1 flex flex-col ${isRTL ? 'text-right' : 'text-left'}`}>
          <div className="space-y-2">
            <h3 className="text-xl font-bold text-gray-900 group-hover:text-blue-600 transition-colors">
              {displayDoctor.displayName}
            </h3>
            <p className="text-blue-600 font-medium">
              {doctor.specialty}
            </p>
          </div>

          <div className="flex items-center text-gray-600 text-sm">
            <MapPin className="h-4 w-4 mr-2 rtl:ml-2 rtl:mr-0" />
            {displayDoctor.displayClinic}
          </div>

          <div className="flex items-center text-gray-600 text-sm">
            <Clock className="h-4 w-4 mr-2 rtl:ml-2 rtl:mr-0" />
            {displayDoctor.displayWorkingHours}
          </div>

          <div className="text-sm text-gray-500 bg-gray-50 p-2 rounded-lg min-h-[40px]">
            {doctor.notes || t("doctor.no_additional_info")}
          </div>

          <div className="flex space-x-3 rtl:space-x-reverse pt-2 mt-auto">
            <Button
              variant="outline"
              size="sm"
              className="flex-1 hover:bg-gray-50 transition-colors bg-transparent"
              asChild
            >
              <Link href={`/${locale}/doctors/${doctor._id}`}>
                {t("doctor.view_profile")}
                <ArrowRight className="h-4 w-4 ml-2 rtl:mr-2 rtl:ml-0" />
              </Link>
            </Button>
          </div>
        </div>
      </div>
    </div>
  )
}
