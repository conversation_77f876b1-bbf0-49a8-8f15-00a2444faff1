"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { useLanguage } from "@/components/providers/language-provider"
import { Star, MapPin, Users, ArrowRight, Building2 } from "lucide-react"
import Image from "next/image"
import Link from "next/link"

interface Clinic {
  id: string
  name: string
  specialties: string[]
  rating: number
  reviewCount: number
  location: string
  image: string
  doctorCount: number
}

interface ClinicCardProps {
  clinic: Clinic
}

export function ClinicCard({ clinic }: ClinicCardProps) {
  const { t } = useLanguage()

  return (
    <div className="group bg-white rounded-3xl shadow-lg border border-gray-100 overflow-hidden hover-lift hover:shadow-2xl transition-all duration-300">
      {/* Clinic Image */}
      <div className="relative h-48 bg-gradient-to-br from-blue-50 to-purple-50">
        <Image
          src={clinic.image || "/doctors-placeholder.png"}
          alt={clinic.name}
          fill
          className="object-cover group-hover:scale-105 transition-transform duration-300"
        />

        {/* Rating Badge */}
        <div className="absolute top-4 left-4">
          <div className="flex items-center bg-white/90 backdrop-blur-sm rounded-full px-3 py-1 shadow-lg">
            <Star className="h-4 w-4 text-yellow-400 fill-current" />
            <span className="text-sm font-semibold text-gray-900 ml-1 rtl:mr-1 rtl:ml-0">{clinic.rating}</span>
          </div>
        </div>

        {/* Doctor Count Badge */}
        <div className="absolute top-4 right-4">
          <div className="flex items-center bg-blue-500 text-white rounded-full px-3 py-1 shadow-lg">
            <Users className="h-3 w-3 mr-1 rtl:ml-1 rtl:mr-0" />
            <span className="text-sm font-medium">{clinic.doctorCount}</span>
          </div>
        </div>
      </div>

      {/* Card Content */}
      <div className="p-6 space-y-4">
        <div className="space-y-2">
          <h3 className="text-xl font-bold text-gray-900 group-hover:text-blue-600 transition-colors">{clinic.name}</h3>

          <div className="flex items-center text-gray-600 text-sm">
            <MapPin className="h-4 w-4 mr-2 rtl:ml-2 rtl:mr-0" />
            {clinic.location}
          </div>
        </div>

        <div className="flex items-center text-sm text-gray-500">
          <Building2 className="h-4 w-4 mr-2 rtl:ml-2 rtl:mr-0" />
          {clinic.reviewCount} {t("common.reviews")}
        </div>

        {/* Specialties */}
        <div className="flex flex-wrap gap-2">
          {clinic.specialties.slice(0, 3).map((specialty, index) => (
            <Badge key={index} variant="secondary" className="text-xs bg-blue-50 text-blue-700 hover:bg-blue-100">
              {specialty}
            </Badge>
          ))}
          {clinic.specialties.length > 3 && (
            <Badge variant="secondary" className="text-xs bg-gray-100 text-gray-600">
              +{clinic.specialties.length - 3} more
            </Badge>
          )}
        </div>

        <Button
          className="w-full bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 shadow-lg hover:shadow-xl transition-all duration-200 group"
          asChild
        >
          <Link href={`/clinics/${clinic.id}`}>
            View Details
            <ArrowRight className="h-4 w-4 ml-2 rtl:mr-2 rtl:ml-0 group-hover:translate-x-1 rtl:group-hover:-translate-x-1 transition-transform" />
          </Link>
        </Button>
      </div>
    </div>
  )
}
