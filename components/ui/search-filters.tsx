"use client"

import { useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Checkbox } from "@/components/ui/checkbox"
import { Label } from "@/components/ui/label"
import { Slider } from "@/components/ui/slider"
import { useLanguage } from "@/components/providers/language-provider"
import { useQuery } from "convex/react"
import { api } from "@/convex/_generated/api"
import { Filter, X } from "lucide-react"

export function SearchFilters() {
  const { t } = useLanguage()
  const [priceRange, setPriceRange] = useState([100, 500])
  const [selectedSpecialties, setSelectedSpecialties] = useState<string[]>([])
  const [selectedCities, setSelectedCities] = useState<string[]>([])
  const [selectedGender, setSelectedGender] = useState<string[]>([])
  const [availableToday, setAvailableToday] = useState(false)
  
  // Get real data from Convex
  const doctors = useQuery(api.doctors.getAllDoctors)
  
  // Extract unique specialties and cities from real doctor data
  const specialties = doctors ? [...new Set(doctors.map(d => d.specialty))].map((specialty, index) => ({
    id: String(index + 1),
    name: specialty,
    doctorCount: doctors.filter(d => d.specialty === specialty).length
  })) : []
  
  const cities = doctors ? [...new Set(doctors.map(d => d.address).filter(Boolean))].map((city, index) => ({
    id: String(index + 1),
    name: city!,
    doctorCount: doctors.filter(d => d.address === city).length
  })) : []

  const handleSpecialtyChange = (specialtyId: string, checked: boolean) => {
    if (checked) {
      setSelectedSpecialties([...selectedSpecialties, specialtyId])
    } else {
      setSelectedSpecialties(selectedSpecialties.filter((id) => id !== specialtyId))
    }
  }

  const clearFilters = () => {
    setSelectedSpecialties([])
    setSelectedCities([])
    setSelectedGender([])
    setAvailableToday(false)
    setPriceRange([100, 500])
  }

  return (
    <div className="bg-white rounded-lg shadow-sm border p-6 space-y-6">
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-semibold text-gray-900 flex items-center">
          <Filter className="h-5 w-5 mr-2 rtl:ml-2 rtl:mr-0" />
          Filters
        </h3>
        <Button variant="ghost" size="sm" onClick={clearFilters}>
          <X className="h-4 w-4 mr-1 rtl:ml-1 rtl:mr-0" />
          Clear
        </Button>
      </div>

      {/* Specialty Filter */}
      <div className="space-y-3">
        <h4 className="font-medium text-gray-900">Specialty</h4>
        <div className="space-y-2 max-h-48 overflow-y-auto">
          {specialties.map((specialty) => (
            <div key={specialty.id} className="flex items-center space-x-2 rtl:space-x-reverse">
              <Checkbox
                id={`specialty-${specialty.id}`}
                checked={selectedSpecialties.includes(specialty.id)}
                onCheckedChange={(checked) => handleSpecialtyChange(specialty.id, checked as boolean)}
              />
              <Label htmlFor={`specialty-${specialty.id}`} className="text-sm text-gray-700 cursor-pointer flex-1">
                {specialty.name} ({specialty.doctorCount})
              </Label>
            </div>
          ))}
        </div>
      </div>

      {/* City Filter */}
      <div className="space-y-3">
        <h4 className="font-medium text-gray-900">City</h4>
        <div className="space-y-2">
          {cities.map((city) => (
            <div key={city.id} className="flex items-center space-x-2 rtl:space-x-reverse">
              <Checkbox
                id={`city-${city.id}`}
                checked={selectedCities.includes(city.id)}
                onCheckedChange={(checked) => {
                  if (checked) {
                    setSelectedCities([...selectedCities, city.id])
                  } else {
                    setSelectedCities(selectedCities.filter((id) => id !== city.id))
                  }
                }}
              />
              <Label htmlFor={`city-${city.id}`} className="text-sm text-gray-700 cursor-pointer flex-1">
                {city.name} ({city.doctorCount})
              </Label>
            </div>
            ))}
        </div>
      </div>

      {/* Gender Filter */}
      <div className="space-y-3">
        <h4 className="font-medium text-gray-900">Gender</h4>
        <div className="space-y-2">
          {["male", "female"].map((gender) => (
            <div key={gender} className="flex items-center space-x-2 rtl:space-x-reverse">
              <Checkbox
                id={`gender-${gender}`}
                checked={selectedGender.includes(gender)}
                onCheckedChange={(checked) => {
                  if (checked) {
                    setSelectedGender([...selectedGender, gender])
                  } else {
                    setSelectedGender(selectedGender.filter((g) => g !== gender))
                  }
                }}
              />
              <Label htmlFor={`gender-${gender}`} className="text-sm text-gray-700 cursor-pointer flex-1 capitalize">
                {gender}
              </Label>
            </div>
          ))}
        </div>
      </div>

      {/* Availability Filter */}
      <div className="space-y-3">
        <h4 className="font-medium text-gray-900">Availability</h4>
        <div className="flex items-center space-x-2 rtl:space-x-reverse">
          <Checkbox id="available-today" checked={availableToday} onCheckedChange={(checked) => setAvailableToday(checked === true)} />
          <Label htmlFor="available-today" className="text-sm text-gray-700 cursor-pointer">
            Available Today
          </Label>
        </div>
      </div>

      {/* Price Range Filter */}
      <div className="space-y-3">
        <h4 className="font-medium text-gray-900">Price Range</h4>
        <div className="px-2">
          <Slider value={priceRange} onValueChange={setPriceRange} max={1000} min={50} step={25} className="w-full" />
          <div className="flex justify-between text-sm text-gray-600 mt-2">
            <span>
              {priceRange[0]} {t("common.egp")}
            </span>
            <span>
              {priceRange[1]} {t("common.egp")}
            </span>
          </div>
        </div>
      </div>

      {/* Apply Filters Button */}
      <Button className="w-full medical-primary">Apply Filters</Button>
    </div>
  )
}
