"use client"

import { useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Checkbox } from "@/components/ui/checkbox"
import { Label } from "@/components/ui/label"
import { Slider } from "@/components/ui/slider"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>er, <PERSON><PERSON><PERSON>it<PERSON> } from "@/components/ui/sheet"
import { useLanguage } from "@/components/providers/language-provider"
import { useQuery } from "convex/react"
import { api } from "@/convex/_generated/api"
import { Filter } from "lucide-react"

interface MobileFiltersProps {
  isOpen: boolean
  onClose: () => void
}

export function MobileFilters({ isOpen, onClose }: MobileFiltersProps) {
  const { t } = useLanguage()
  const [priceRange, setPriceRange] = useState([100, 500])
  const [selectedSpecialties, setSelectedSpecialties] = useState<string[]>([])
  const [selectedCities, setSelectedCities] = useState<string[]>([])
  const [availableToday, setAvailableToday] = useState(false)
  
  // Get real data from Convex
  const doctors = useQuery(api.doctors.getAllDoctors)
  
  // Extract unique specialties and cities from real doctor data
  const specialties = doctors ? [...new Set(doctors.map(d => d.specialty))].map((specialty, index) => ({
    id: String(index + 1),
    name: specialty,
    doctorCount: doctors.filter(d => d.specialty === specialty).length
  })) : []
  
  const cities = doctors ? [...new Set(doctors.map(d => d.address).filter(Boolean))].map((city, index) => ({
    id: String(index + 1),
    name: city!,
    doctorCount: doctors.filter(d => d.address === city).length
  })) : []

  const clearFilters = () => {
    setSelectedSpecialties([])
    setSelectedCities([])
    setAvailableToday(false)
    setPriceRange([100, 500])
  }

  return (
    <Sheet open={isOpen} onOpenChange={onClose}>
      <SheetContent side="bottom" className="h-[80vh] rounded-t-3xl">
        <SheetHeader className="pb-6 border-b border-gray-100">
          <div className="flex items-center justify-between">
            <SheetTitle className="text-2xl font-bold text-gray-900 flex items-center">
              <Filter className="h-6 w-6 mr-3 rtl:ml-3 rtl:mr-0" />
              Filters
            </SheetTitle>
            <Button variant="ghost" size="sm" onClick={clearFilters}>
              Clear All
            </Button>
          </div>
        </SheetHeader>

        <div className="py-6 space-y-8 custom-scrollbar overflow-y-auto">
          {/* Specialty Filter */}
          <div className="space-y-4">
            <h4 className="text-lg font-semibold text-gray-900">Specialty</h4>
            <div className="grid grid-cols-2 gap-3">
              {specialties.map((specialty) => (
                <div
                  key={specialty.id}
                  className="flex items-center space-x-3 rtl:space-x-reverse p-3 bg-gray-50 rounded-xl"
                >
                  <Checkbox
                    id={`specialty-${specialty.id}`}
                    checked={selectedSpecialties.includes(specialty.id)}
                    onCheckedChange={(checked) => {
                      if (checked) {
                        setSelectedSpecialties([...selectedSpecialties, specialty.id])
                      } else {
                        setSelectedSpecialties(selectedSpecialties.filter((id) => id !== specialty.id))
                      }
                    }}
                  />
                  <Label htmlFor={`specialty-${specialty.id}`} className="text-sm font-medium cursor-pointer flex-1">
                    {specialty.name}
                  </Label>
                </div>
              ))}
            </div>
          </div>

          {/* City Filter */}
          <div className="space-y-4">
            <h4 className="text-lg font-semibold text-gray-900">City</h4>
            <div className="space-y-3">
              {cities.map((city) => (
                <div
                  key={city.id}
                  className="flex items-center space-x-3 rtl:space-x-reverse p-3 bg-gray-50 rounded-xl"
                >
                  <Checkbox
                    id={`city-${city.id}`}
                    checked={selectedCities.includes(city.id)}
                    onCheckedChange={(checked) => {
                      if (checked) {
                        setSelectedCities([...selectedCities, city.id])
                      } else {
                        setSelectedCities(selectedCities.filter((id) => id !== city.id))
                      }
                    }}
                  />
                  <Label htmlFor={`city-${city.id}`} className="text-sm font-medium cursor-pointer flex-1">
                    {city.name} ({city.doctorCount} doctors)
                  </Label>
                </div>
              ))}
            </div>
          </div>

          {/* Availability Filter */}
          <div className="space-y-4">
            <h4 className="text-lg font-semibold text-gray-900">Availability</h4>
            <div className="flex items-center space-x-3 rtl:space-x-reverse p-3 bg-gray-50 rounded-xl">
              <Checkbox id="available-today" checked={availableToday} onCheckedChange={(checked) => setAvailableToday(checked === true)} />
              <Label htmlFor="available-today" className="text-sm font-medium cursor-pointer">
                Available Today
              </Label>
            </div>
          </div>

          {/* Price Range Filter */}
          <div className="space-y-4">
            <h4 className="text-lg font-semibold text-gray-900">Price Range</h4>
            <div className="px-4 py-6 bg-gray-50 rounded-xl">
              <Slider
                value={priceRange}
                onValueChange={setPriceRange}
                max={1000}
                min={50}
                step={25}
                className="w-full"
              />
              <div className="flex justify-between text-sm text-gray-600 mt-4">
                <span>
                  {priceRange[0]} {t("common.egp")}
                </span>
                <span>
                  {priceRange[1]} {t("common.egp")}
                </span>
              </div>
            </div>
          </div>
        </div>

        {/* Apply Button */}
        <div className="border-t border-gray-100 pt-6">
          <Button
            className="w-full h-14 text-lg bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 shadow-lg hover:shadow-xl transition-all duration-200"
            onClick={onClose}
          >
            Apply Filters
          </Button>
        </div>
      </SheetContent>
    </Sheet>
  )
}
