"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Calendar } from "@/components/ui/calendar"
import { useLanguage } from "@/components/providers/language-provider"
import { CalendarIcon, Clock, CreditCard } from "lucide-react"
import { <PERSON> } from "@/types"

interface BookingWidgetProps {
  doctor: Doctor
}

export function BookingWidget({ doctor }: BookingWidgetProps) {
  const { t } = useLanguage()
  const [selectedDate, setSelectedDate] = useState<Date | undefined>(new Date())
  const [selectedTime, setSelectedTime] = useState<string>("")

  const availableTimes = [
    "09:00 AM",
    "09:30 AM",
    "10:00 AM",
    "10:30 AM",
    "11:00 AM",
    "11:30 AM",
    "02:00 PM",
    "02:30 PM",
    "03:00 PM",
    "03:30 PM",
    "04:00 PM",
    "04:30 PM",
  ]

  return (
    <div className="bg-white rounded-lg shadow-sm border p-6 space-y-6 sticky top-8">
      <div className="text-center">
        <h3 className="text-xl font-semibold text-gray-900 mb-2">Book Appointment</h3>
        <div className="text-2xl font-bold text-blue-600">
          Consultation Fee
        </div>
        <p className="text-sm text-gray-600">Contact for pricing</p>
      </div>

      {/* Calendar */}
      <div className="space-y-4">
        <h4 className="font-medium text-gray-900 flex items-center">
          <CalendarIcon className="h-4 w-4 mr-2 rtl:ml-2 rtl:mr-0" />
          Select Date
        </h4>
        <Calendar
          mode="single"
          selected={selectedDate}
          onSelect={setSelectedDate}
          disabled={(date) => date < new Date()}
          className="rounded-md border"
        />
      </div>

      {/* Time Slots */}
      {selectedDate && (
        <div className="space-y-4">
          <h4 className="font-medium text-gray-900 flex items-center">
            <Clock className="h-4 w-4 mr-2 rtl:ml-2 rtl:mr-0" />
            Available Times
          </h4>
          <div className="grid grid-cols-2 gap-2">
            {availableTimes.map((time) => (
              <Button
                key={time}
                variant={selectedTime === time ? "default" : "outline"}
                size="sm"
                onClick={() => setSelectedTime(time)}
                className="text-xs"
              >
                {time}
              </Button>
            ))}
          </div>
        </div>
      )}

      {/* Booking Summary */}
      {selectedDate && selectedTime && (
        <div className="bg-gray-50 rounded-lg p-4 space-y-2">
          <h4 className="font-medium text-gray-900">Booking Summary</h4>
          <div className="text-sm space-y-1">
            <div className="flex justify-between">
              <span className="text-gray-600">Date:</span>
              <span>{selectedDate.toLocaleDateString()}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-600">Time:</span>
              <span>{selectedTime}</span>
            </div>
          </div>
        </div>
      )}

      {/* Book Button */}
      <Button 
        className="w-full" 
        size="lg"
        disabled={!selectedDate || !selectedTime}
      >
        <CreditCard className="h-4 w-4 mr-2 rtl:ml-2 rtl:mr-0" />
        Book Appointment
      </Button>
    </div>
  )
}
