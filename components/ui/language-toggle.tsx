"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { useLanguage } from "@/components/providers/language-provider"
import { Globe } from "lucide-react"

export function LanguageToggle() {
  const { language, switchLanguage } = useLanguage()

  return (
    <Button
      variant="ghost"
      size="sm"
      onClick={() => switchLanguage(language === "en" ? "ar" : "en")}
      className="flex items-center space-x-1 rtl:space-x-reverse text-gray-700 hover:text-blue-600 hover:bg-blue-50/80 rounded-xl transition-all duration-300"
    >
      <Globe className="h-4 w-4" />
      <span className="text-sm font-medium">{language === "en" ? "العربية" : "English"}</span>
    </Button>
  )
}
