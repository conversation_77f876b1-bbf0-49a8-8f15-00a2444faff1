"use client"

import Link from "next/link"
import { Stethoscope, Facebook, Twitter, Instagram } from "lucide-react"
import { useLanguage } from "@/components/providers/language-provider"
import { useParams } from "next/navigation"

export function Footer() {
  const { t } = useLanguage()
  const params = useParams()
  const locale = params.locale || 'en'

  return (
    <footer className="bg-gray-900 text-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {/* Company Info */}
          <div className="space-y-4">
            <div className="flex items-center space-x-2 rtl:space-x-reverse">
              <Stethoscope className="h-8 w-8 text-blue-400" />
              <span className="text-xl font-bold">HealthCare Sudan</span>
            </div>
            <p className="text-gray-300 text-sm">
              Connecting Sudanese patients with trusted doctors in Egypt. Book appointments easily and get the care you
              deserve.
            </p>
            <div className="flex space-x-4 rtl:space-x-reverse">
              <Facebook className="h-5 w-5 text-gray-400 hover:text-blue-400 cursor-pointer" />
              <Twitter className="h-5 w-5 text-gray-400 hover:text-blue-400 cursor-pointer" />
              <Instagram className="h-5 w-5 text-gray-400 hover:text-blue-400 cursor-pointer" />
            </div>
          </div>

          {/* Company Links */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold">Company</h3>
            <div className="space-y-2">
              <Link href={`/${locale}/about`} className="block text-gray-300 hover:text-white text-sm">
                {t("footer.about_us")}
              </Link>
              <Link href={`/${locale}/team`} className="block text-gray-300 hover:text-white text-sm">
                {t("footer.our_team")}
              </Link>
              <Link href={`/${locale}/careers`} className="block text-gray-300 hover:text-white text-sm">
                {t("footer.careers")}
              </Link>
              <Link href={`/${locale}/contact`} className="block text-gray-300 hover:text-white text-sm">
                {t("footer.contact")}
              </Link>
            </div>
          </div>

          {/* Services */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold">Services</h3>
            <div className="space-y-2">
              <Link href={`/${locale}/doctors`} className="block text-gray-300 hover:text-white text-sm">
                Find Doctors
              </Link>
              <Link href={`/${locale}/clinics`} className="block text-gray-300 hover:text-white text-sm">
                Find Clinics
              </Link>
              <Link href={`/${locale}/auth/doctor-signup`} className="block text-gray-300 hover:text-white text-sm">
                {t("footer.doctor_signup")}
              </Link>
              <Link href={`/${locale}/help`} className="block text-gray-300 hover:text-white text-sm">
                {t("footer.help")}
              </Link>
            </div>
          </div>
        </div>

        <div className="border-t border-gray-800 mt-8 pt-8 flex flex-col md:flex-row justify-between items-center">
          <p className="text-gray-400 text-sm">© 2024 HealthCare Sudan. All rights reserved.</p>
          <div className="flex space-x-6 rtl:space-x-reverse mt-4 md:mt-0">
            <Link href={`/${locale}/terms`} className="text-gray-400 hover:text-white text-sm">
              {t("footer.terms")}
            </Link>
            <Link href={`/${locale}/privacy`} className="text-gray-400 hover:text-white text-sm">
              {t("footer.privacy")}
            </Link>
          </div>
        </div>
      </div>
    </footer>
  )
}
