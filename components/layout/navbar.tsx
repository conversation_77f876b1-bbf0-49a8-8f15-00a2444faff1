"use client"

import { useState, useEffect } from "react"
import Link from "next/link"
import { useRouter } from "next/navigation"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { LanguageToggle } from "@/components/ui/language-toggle"
import { useLanguage } from "@/components/providers/language-provider"
import { Menu, X, User, Calendar, Sparkles, LogOut, Settings, ChevronDown } from "lucide-react"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuSeparator, DropdownMenuTrigger } from "@/components/ui/dropdown-menu"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"

export function Navbar() {
  const { t, isRTL, language } = useLanguage()
  const router = useRouter()
  const [isMenuOpen, setIsMenuOpen] = useState(false)
  const [isScrolled, setIsScrolled] = useState(false)
  const [isLoggedIn, setIsLoggedIn] = useState(false)
  const [userInfo, setUserInfo] = useState<any>(null)

  // Function to check auth status
  const checkAuthStatus = async () => {
    try {
      const response = await fetch('/api/auth/check', { method: 'GET' });
      
      if (response.ok) {
        const data = await response.json();
        setIsLoggedIn(true);
        setUserInfo(data.user);
      } else {
        setIsLoggedIn(false);
        setUserInfo(null);
      }
    } catch (error) {
      console.error("💥 Auth check error:", error);
      setIsLoggedIn(false);
      setUserInfo(null);
    }
  }

  useEffect(() => {
    // Check if user is logged in by checking for session cookie
    checkAuthStatus();

    // Listen for login events to refresh auth state
    const handleLoginEvent = () => {
      console.log("🔔 Login event received, refreshing auth state...");
      checkAuthStatus();
    };

    // Listen for custom login event
    window.addEventListener('user-login', handleLoginEvent);
    
    // Also listen for storage events (in case login happens in another tab)
    window.addEventListener('storage', (e) => {
      if (e.key === 'session') {
        checkAuthStatus();
      }
    });

    // Listen for route changes to refresh auth state
    const handleRouteChange = () => {
      checkAuthStatus();
    };

    // Check auth status when component mounts and when route changes
    window.addEventListener('popstate', handleRouteChange);
    
    // Cleanup listeners
    return () => {
      window.removeEventListener('user-login', handleLoginEvent);
      window.removeEventListener('storage', checkAuthStatus);
      window.removeEventListener('popstate', handleRouteChange);
    };
  }, [])

  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 20)
    }
    window.addEventListener("scroll", handleScroll)
    return () => window.removeEventListener("scroll", handleScroll)
  }, [])

  const handleLogout = async () => {
    try {
      await fetch('/api/auth/logout', { method: 'POST' })
      setIsLoggedIn(false)
      setUserInfo(null)
      // Redirect to home page
      window.location.href = `/${language}`
    } catch (error) {
      console.error('Logout failed:', error)
    }
  }

  const getInitials = (firstName: string, lastName: string) => {
    return `${firstName?.charAt(0) || ''}${lastName?.charAt(0) || ''}`.toUpperCase()
  }

  return (
    <>
      <nav
        className={`relative z-50 transition-all duration-500 ${
          isScrolled 
            ? "bg-white/95 backdrop-blur-xl shadow-2xl shadow-blue-500/10 border-b border-blue-100/50" 
            : "bg-white/70 backdrop-blur-2xl border-b border-transparent"
        }`}
      >
        <div className="max-w-7xl mx-auto px-6 lg:px-8">
          <div className="flex justify-between items-center h-20">
            {/* Logo */}
            <Link href={`/${language}`} className="flex items-center space-x-4 rtl:space-x-reverse group">
              <div className="relative">
                <img 
                  src="/favicon.png" 
                  alt="HealthCare Sudan Logo" 
                  className="h-16 w-16 object-contain"
                />
              </div>
              <div className="hidden sm:block">
                <span className="text-2xl font-bold bg-gradient-to-r from-gray-900 via-blue-800 to-purple-700 bg-clip-text text-transparent">
                  Medical Sudan
                </span>
              </div>
            </Link>

            {/* Desktop Navigation */}
            <div className="hidden lg:flex items-center space-x-8 rtl:space-x-reverse">
              <Link
                href={`/${language}/doctors`}
                className="relative text-gray-700 hover:text-blue-600 font-medium transition-all duration-300 group"
              >
                {t("nav.doctors")}
                <span className="absolute -bottom-1 left-0 w-0 h-0.5 bg-gradient-to-r from-blue-500 to-purple-500 group-hover:w-full transition-all duration-300"></span>
              </Link>
              <Link
                href={`/${language}/clinics`}
                className="relative text-gray-700 hover:text-blue-600 font-medium transition-all duration-300 group"
              >
                {t("nav.clinics")}
                <span className="absolute -bottom-1 left-0 w-0 h-0.5 bg-gradient-to-r from-blue-500 to-purple-500 group-hover:w-full transition-all duration-300"></span>
              </Link>
              <LanguageToggle />
              
              {/* Conditional Auth Buttons / Profile */}
              {!isLoggedIn ? (
                <>
                  <Button 
                    variant="ghost" 
                    size="sm" 
                    className="text-gray-700 hover:text-blue-600 hover:bg-blue-50/80 rounded-xl transition-all duration-300" 
                    asChild
                  >
                    <Link href={`/${language}/auth/login`}>
                      <User className="h-4 w-4 mr-2 rtl:ml-2 rtl:mr-0" />
                      {t("nav.login")}
                    </Link>
                  </Button>
                  <Button
                    size="sm"
                    className="bg-gradient-to-r from-blue-500 via-blue-600 to-purple-600 hover:from-blue-600 hover:via-blue-700 hover:to-purple-700 shadow-lg hover:shadow-xl hover:scale-105 transition-all duration-300 rounded-xl px-6"
                    asChild
                  >
                    <Link href={`/${language}/auth/signup`}>
                      <Calendar className="h-4 w-4 mr-2 rtl:ml-2 rtl:mr-0" />
                      {t("nav.signup")}
                    </Link>
                  </Button>
                </>
              ) : (
                <>
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button
                        variant="ghost"
                        size="sm"
                        className="flex items-center space-x-2 rtl:space-x-reverse hover:bg-blue-50/80 rounded-xl transition-all duration-300"
                      >
                        <Avatar className="h-8 w-8">
                          <AvatarImage src={userInfo?.image} alt={userInfo?.firstName} />
                          <AvatarFallback className="bg-blue-100 text-blue-600 font-medium">
                            {getInitials(userInfo?.firstName || '', userInfo?.lastName || '')}
                          </AvatarFallback>
                        </Avatar>
                        <span className="hidden sm:block text-gray-700 font-medium">
                          {userInfo?.firstName} {userInfo?.lastName}
                        </span>
                        <ChevronDown className="h-4 w-4 text-gray-500" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end" className="w-56">
                      <div className="px-3 py-2">
                        <p className="text-sm font-medium text-gray-900">
                          {userInfo?.firstName} {userInfo?.lastName}
                        </p>
                        <p className="text-sm text-gray-500">{userInfo?.email}</p>
                      </div>
                      <DropdownMenuSeparator />
                      <DropdownMenuItem asChild>
                        <Link href={`/${language}/profile`} className="cursor-pointer">
                          <User className="h-4 w-4 mr-2" />
                          Profile
                        </Link>
                      </DropdownMenuItem>
                      <DropdownMenuItem asChild>
                        <Link href={`/${language}/dashboard`} className="cursor-pointer">
                          <Settings className="h-4 w-4 mr-2" />
                          Dashboard
                        </Link>
                      </DropdownMenuItem>
                      <DropdownMenuSeparator />
                      <DropdownMenuItem onClick={handleLogout} className="cursor-pointer text-red-600 hover:text-red-700">
                        <LogOut className="h-4 w-4 mr-2" />
                        Logout
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </>
              )}
            </div>

            {/* Mobile menu button */}
            <div className="lg:hidden flex items-center space-x-3 rtl:space-x-reverse">
              <LanguageToggle />
              <Button 
                variant="ghost" 
                size="sm" 
                onClick={() => setIsMenuOpen(!isMenuOpen)} 
                className="p-2 hover:bg-blue-50/80 rounded-xl transition-all duration-300"
              >
                {isMenuOpen ? <X className="h-5 w-5" /> : <Menu className="h-5 w-5" />}
              </Button>
            </div>
          </div>
        </div>

        {/* Mobile menu */}
        {isMenuOpen && (
          <div className="lg:hidden animate-in slide-in-from-top-2 duration-300">
            <div className="bg-white/95 backdrop-blur-xl border-t border-blue-100/50 shadow-lg">
              <div className="px-6 py-8 space-y-6">
                <div className="space-y-3">
                  <Link
                    href={`/${language}/doctors`}
                    className="block px-6 py-4 text-gray-700 hover:text-blue-600 hover:bg-gradient-to-r hover:from-blue-50/50 hover:to-purple-50/50 rounded-2xl transition-all duration-300 font-medium"
                    onClick={() => setIsMenuOpen(false)}
                  >
                    {t("nav.doctors")}
                  </Link>
                  <Link
                    href={`/${language}/clinics`}
                    className="block px-6 py-4 text-gray-700 hover:text-blue-600 hover:bg-gradient-to-r hover:from-blue-50/50 hover:to-purple-50/50 rounded-2xl transition-all duration-300 font-medium"
                    onClick={() => setIsMenuOpen(false)}
                  >
                    {t("nav.clinics")}
                  </Link>
                </div>

                {/* Conditional Mobile Auth Buttons / Profile */}
                {!isLoggedIn ? (
                  <>
                    <div className="flex space-x-4 rtl:space-x-reverse pt-6 border-t border-blue-100/50">
                      <Button 
                        variant="outline" 
                        size="sm" 
                        className="flex-1 bg-transparent border-blue-200 hover:bg-blue-50/80 hover:border-blue-300 rounded-xl transition-all duration-300" 
                        asChild
                      >
                        <Link href={`/${language}/auth/login`}>{t("nav.login")}</Link>
                      </Button>
                      <Button 
                        size="sm" 
                        className="flex-1 bg-gradient-to-r from-blue-500 via-blue-600 to-purple-600 hover:from-blue-600 hover:via-blue-700 hover:to-purple-700 rounded-xl transition-all duration-300" 
                        asChild
                      >
                        <Link href={`/${language}/auth/signup`}>{t("nav.signup")}</Link>
                      </Button>
                    </div>
                  </>
                ) : (
                  <>
                    <div className="pt-6 border-t border-blue-100/50 space-y-3">
                      <div className="px-6 py-4 bg-blue-50/50 rounded-2xl">
                        <p className="text-sm font-medium text-gray-900">
                          {userInfo?.firstName} {userInfo?.lastName}
                        </p>
                        <p className="text-sm text-gray-500">{userInfo?.email}</p>
                      </div>
                      <div className="space-y-2">
                        <Button 
                          variant="outline" 
                          size="sm" 
                          className="w-full bg-transparent border-blue-200 hover:bg-blue-50/80 hover:border-blue-300 rounded-xl transition-all duration-300" 
                          asChild
                        >
                          <Link href={`/${language}/profile`}>Profile</Link>
                        </Button>
                        <Button 
                          variant="outline" 
                          size="sm" 
                          className="w-full bg-transparent border-blue-200 hover:bg-blue-50/80 hover:border-blue-300 rounded-xl transition-all duration-300" 
                          asChild
                        >
                          <Link href={`/${language}/dashboard`}>Dashboard</Link>
                        </Button>
                        <Button 
                          variant="outline" 
                          size="sm" 
                          className="w-full bg-transparent border-red-200 hover:bg-red-50/80 hover:border-red-300 text-red-600 hover:text-red-700 rounded-xl transition-all duration-300" 
                          onClick={handleLogout}
                        >
                          <LogOut className="h-4 w-4 mr-2" />
                          Logout
                        </Button>
                      </div>
                    </div>
                  </>
                )}
              </div>
            </div>
          </div>
        )}
      </nav>
    </>
  )
}
