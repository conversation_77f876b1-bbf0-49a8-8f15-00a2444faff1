"use client"

import { ConvexProvider, ConvexReactClient } from "convex/react"
import { useMemo } from "react"

export function ConvexProviderWrapper({ children }: { children: React.ReactNode }) {
  const convex = useMemo(() => {
    return new ConvexReactClient(process.env.NEXT_PUBLIC_CONVEX_URL as string)
  }, [])

  return (
    <ConvexProvider client={convex}>
      {children}
    </ConvexProvider>
  )
}
