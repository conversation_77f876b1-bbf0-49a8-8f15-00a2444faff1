"use client"

import type React from "react"
import { createContext, useContext, useState, useEffect } from "react"
import { useRouter, usePathname } from "next/navigation"

type Language = "en" | "ar"

interface LanguageContextType {
  language: Language
  setLanguage: (lang: Language) => void
  t: (key: string, params?: Record<string, string | number>) => string
  isRTL: boolean
  switchLanguage: (lang: Language) => void
}

const LanguageContext = createContext<LanguageContextType | undefined>(undefined)

const translations = {
  en: {
    // Navigation
    "nav.home": "Home",
    "nav.doctors": "Doctors",
    "nav.clinics": "Clinics",
    "nav.about": "About",
    "nav.login": "Login",
    "nav.signup": "Sign Up",
    "nav.patient_login": "Patient Login",
    "nav.doctor_login": "Doctor Login",

    // Hero Section
    "hero.title": "Find & Book the Best Sudanese Doctors in Egypt",
    "hero.subtitle":
    "Book appointments with verified Sudanese doctors. Read reviews, compare prices, and book instantly.",
    "hero.find_and_book": "Find & Book the",
    "hero.doctors": "Doctors",
    "hero.specialists": "Specialists",
    "hero.experts": "Experts",
    "hero.clinicians": "Clinicians",
    "hero.search_placeholder": "Search by doctor name or specialty",
    "hero.search_by_name": "Doctor name or hospital",
    "hero.search_telehealth_specialists": "Doctor name or hospital",
    "hero.in_this_city": "Choose city",
    "hero.in_this_area": "Choose area",
    "hero.select_specialist": "Choose specialty",
    "hero.find_telehealth": "Search",
    "hero.in_person_visits": "Examination or procedure",
    "hero.call_consultations": "Call consultation with doctor",
    "hero.specialty": "Specialty",
    "hero.city": "City",
    "hero.area": "Area",
    "hero.insurance": "Insurance",
    "hero.search": "Search",
    "hero.book_doctor": "Book a doctor",
    "hero.telehealth": "Telehealth",

    // Common
    "common.book_now": "Book Now",
    "common.view_all": "View All",
    "common.rating": "Rating",
    "common.reviews": "Reviews",
    "common.location": "Location",
    "common.price": "Price",
    "common.available": "Available",
    "common.egp": "EGP",

    // Specialties
    "specialty.dentist": "Dentist",
    "specialty.pediatrician": "Pediatrician",
    "specialty.psychiatrist": "Psychiatrist",
    "specialty.cardiologist": "Cardiologist",
    "specialty.dermatologist": "Dermatologist",
    "specialty.orthopedic": "Orthopedic",
    "specialty.gynecologist": "Gynecologist",
    "specialty.neurologist": "Neurologist",
    "specialty.ophthalmologist": "Ophthalmologist",

    // Cities
    "city.cairo": "Cairo",
    "city.giza": "Giza",
    "city.alexandria": "Alexandria",

    // Areas
    "area.maadi": "Maadi",
    "area.zamalek": "Zamalek",
    "area.heliopolis": "Heliopolis",
    "area.nasr-city": "Nasr City",
    "area.dokki": "Dokki",
    "area.mohandessin": "Mohandessin",
    "area.6th-october": "6th October",
    "area.new-cairo": "New Cairo",

    // Footer
    "footer.about_us": "About Us",
    "footer.our_team": "Our Team",
    "footer.careers": "Careers",
    "footer.contact": "Contact Us",
    "footer.terms": "Terms of Use",
    "footer.privacy": "Privacy Policy",
    "footer.doctor_signup": "Doctor Sign Up",
    "footer.help": "Help",
    
    // Doctor Card
    "doctor.title": "Doctor",
    "doctor.location_not_specified": "Location not specified",
    "doctor.working_hours_not_specified": "Working hours not specified",
    "doctor.no_additional_info": "No additional information available",
    "doctor.view_profile": "View Profile",
    
    // Doctors Page
    "doctors.title": "Find Your Doctor",
    "doctors.subtitle": "Search and book appointments with qualified Sudanese doctors in Egypt",
    "doctors.search_placeholder": "Search by name, specialty, or location...",
    "doctors.sort_by": "Sort by",
    "doctors.sort_best_match": "Best Match",
    "doctors.sort_highest_rated": "Highest Rated",
    "doctors.sort_lowest_price": "Lowest Price",
    "doctors.sort_availability": "Availability",
    "doctors.results_count": "Showing {count} doctors",
  },
  ar: {
    // Navigation
    "nav.home": "الرئيسية",
    "nav.doctors": "الأطباء",
    "nav.clinics": "العيادات",
    "nav.about": "من نحن",
    "nav.login": "تسجيل الدخول",
    "nav.signup": "إنشاء حساب",
    "nav.patient_login": "دخول المريض",
    "nav.doctor_login": "دخول الطبيب",

    // Hero Section
    "hero.title": "ابحث واحجز مع أفضل الأطباء السودانيين في مصر",
    "hero.subtitle": "احجز مواعيد مع أطباء سودانيين معتمدين. اقرأ التقييمات، قارن الأسعار، واحجز فوراً.",
    "hero.find_and_book": "ابحث واحجز",
    "hero.doctors": "الأطباء",
    "hero.specialists": "المتخصصون",
    "hero.experts": "الخبراء",
    "hero.search_placeholder": "ابحث باسم الطبيب أو التخصص",
    "hero.search_by_name": "الدكتور أو المستشفى",
    "hero.search_telehealth_specialists": "الدكتور أو المستشفى",
    "hero.in_this_city": "اختر المحافظة",
    "hero.in_this_area": "اختر المنطقة",
    "hero.select_specialist": "اختر التخصص",
    "hero.find_telehealth": "ابحث",
    "hero.in_person_visits": "الفحص أو الإجراء",
    "hero.call_consultations": "المتابعة عبر مكالمة مع دكتور",
    "hero.specialty": "التخصص",
    "hero.city": "المدينة",
    "hero.area": "المنطقة",
    "hero.insurance": "التأمين",
    "hero.search": "ابحث",
    "hero.book_doctor": "احجز دكتور",
    "hero.telehealth": "مكالمة دكتور",

    // Common
    "common.book_now": "احجز الآن",
    "common.view_all": "عرض الكل",
    "common.rating": "التقييم",
    "common.reviews": "التقييمات",
    "common.location": "الموقع",
    "common.price": "السعر",
    "common.available": "متاح",
    "common.egp": "جنيه",

    // Specialties
    "specialty.dentist": "طبيب أسنان",
    "specialty.pediatrician": "طبيب أطفال",
    "specialty.psychiatrist": "طبيب نفسي",
    "specialty.cardiologist": "طبيب قلب",
    "specialty.dermatologist": "طبيب جلدية",
    "specialty.orthopedic": "طبيب عظام",
    "specialty.gynecologist": "طبيب نساء",
    "specialty.neurologist": "طبيب أعصاب",
    "specialty.ophthalmologist": "طبيب عيون",

    // Cities
    "city.cairo": "القاهرة",
    "city.giza": "الجيزة",
    "city.alexandria": "الإسكندرية",

    // Areas
    "area.maadi": "معادي",
    "area.zamalek": "زمالك",
    "area.heliopolis": "الهيليوبوليس",
    "area.nasr-city": "النصر المدينة",
    "area.dokki": "دقي",
    "area.mohandessin": "المهندسين",
    "area.6th-october": "السادس من أكتوبر",
    "area.new-cairo": "القاهرة الجديدة",

    // Footer
    "footer.about_us": "من نحن",
    "footer.our_team": "فريقنا",
    "footer.careers": "الوظائف",
    "footer.contact": "اتصل بنا",
    "footer.terms": "شروط الاستخدام",
    "footer.privacy": "سياسة الخصوصية",
    "footer.doctor_signup": "تسجيل الأطباء",
    "footer.help": "المساعدة",
    
    // Doctor Card
    "doctor.title": "طبيب",
    "doctor.location_not_specified": "الموقع غير محدد",
    "doctor.working_hours_not_specified": "ساعات العمل غير محددة",
    "doctor.no_additional_info": "لا توجد معلومات إضافية متاحة",
    "doctor.view_profile": "عرض الملف الشخصي",
    
    // Doctors Page
    "doctors.title": "ابحث عن طبيبك",
    "doctors.subtitle": "ابحث واحجز مواعيد مع أطباء سودانيين مؤهلين في مصر",
    "doctors.search_placeholder": "ابحث بالاسم أو التخصص أو الموقع...",
    "doctors.sort_by": "ترتيب حسب",
    "doctors.sort_best_match": "أفضل تطابق",
    "doctors.sort_highest_rated": "أعلى تقييم",
    "doctors.sort_lowest_price": "أقل سعر",
    "doctors.sort_availability": "التوفر",
    "doctors.results_count": "عرض {count} طبيب",
  },
}

interface LanguageProviderProps {
  children: React.ReactNode
  initialLocale: Language
}

export function LanguageProvider({ children, initialLocale }: LanguageProviderProps) {
  const [language, setLanguage] = useState<Language>(initialLocale)
  const [isLoaded, setIsLoaded] = useState(false)
  const router = useRouter()
  const pathname = usePathname()

  // Load saved language preference on mount
  useEffect(() => {
    if (typeof window !== 'undefined') {
      const savedLang = localStorage.getItem('preferred-language') as Language
      if (savedLang && (savedLang === 'en' || savedLang === 'ar')) {
        setLanguage(savedLang)
        
        // If we're not on the correct locale route, redirect
        const currentLocale = pathname.split('/')[1]
        if (currentLocale !== savedLang) {
          const pathWithoutLocale = pathname.replace(/^\/[a-z]{2}/, '') || '/'
          const newPath = `/${savedLang}${pathWithoutLocale}`
          router.push(newPath)
        }
      }
      
      // Add a small delay to prevent flash and make loading feel natural
      const timer = setTimeout(() => {
        setIsLoaded(true)
      }, 100)
      
      return () => clearTimeout(timer)
    } else {
      // Server-side rendering - set loaded immediately
      setIsLoaded(true)
    }
  }, [pathname, router])

  // Update language when initialLocale changes (route changes)
  useEffect(() => {
    if (isLoaded) {
      setLanguage(initialLocale)
      // Save to localStorage
      if (typeof window !== 'undefined') {
        localStorage.setItem('preferred-language', initialLocale)
      }
    }
  }, [initialLocale, isLoaded])

  const switchLanguage = (newLang: Language) => {
    // Save to localStorage
    if (typeof window !== 'undefined') {
      localStorage.setItem('preferred-language', newLang)
    }
    
    // Set cookie for server-side access
    document.cookie = `preferred-language=${newLang}; path=/; max-age=${60 * 60 * 24 * 365}`
    
    // Extract the path without locale
    const pathWithoutLocale = pathname.replace(/^\/[a-z]{2}/, '') || '/'
    const newPath = `/${newLang}${pathWithoutLocale}`
    router.push(newPath)
  }

  const t = (key: string, params?: Record<string, string | number>): string => {
    // Always try to translate, even if not fully loaded
    // This prevents the flash of untranslated content
    
    // Skip translation for known non-translation keys
    if (key === "favicon.ico" || key.includes(".ico") || key.includes(".css") || key.includes(".js")) {
      return key
    }
    
    // Check if the language exists in translations
    if (!translations[language]) {
      console.warn(`Language '${language}' not found in translations`)
      return key
    }
    
    // Check if the key exists in the current language
    let translation = translations[language][key as keyof typeof translations[typeof language]]
    if (translation) {
      // Apply parameter interpolation if params are provided
      if (params) {
        Object.entries(params).forEach(([param, value]) => {
          translation = translation.replace(new RegExp(`{${param}}`, 'g'), String(value))
        })
      }
      return translation
    }
    
    // Fallback to English if the key doesn't exist in current language
    let englishTranslation = translations.en[key as keyof typeof translations.en]
    if (englishTranslation) {
      // Apply parameter interpolation if params are provided
      if (params) {
        Object.entries(params).forEach(([param, value]) => {
          englishTranslation = englishTranslation.replace(new RegExp(`{count}`, 'g'), String(value))
        })
      }
      return englishTranslation
    }
    
    // If key doesn't exist in either language, return the key itself
    console.warn(`Translation key '${key}' not found for language '${language}'`)
    return key
  }

  const isRTL = language === "ar"

  return (
    <LanguageContext.Provider value={{ 
      language, 
      setLanguage, 
      t, 
      isRTL, 
      switchLanguage 
    }}>
      <div className={`no-flash ${isLoaded ? 'loaded' : ''}`}>
        {children}
      </div>
      {!isLoaded && (
        <div className="fixed inset-0 bg-white z-50 flex items-center justify-center">
          <div className="text-center">
            <img 
              src="/Doctor-Symbol-Preloader.gif" 
              alt="Loading..." 
              className="w-16 h-16"
            />
          </div>
        </div>
      )}
    </LanguageContext.Provider>
  )
}

export function useLanguage() {
  const context = useContext(LanguageContext)
  if (context === undefined) {
    throw new Error("useLanguage must be used within a LanguageProvider")
  }
  return context
}
