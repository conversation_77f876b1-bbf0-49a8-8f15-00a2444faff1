import { NextRequest, NextResponse } from 'next/server'

const locales = ['en', 'ar']
const defaultLocale = 'en'

export function middleware(request: NextRequest) {
  const pathname = request.nextUrl.pathname
  const session = request.cookies.get('session')?.value
  
  // Check if the pathname already has a locale
  const pathnameHasLocale = locales.some(
    (locale) => pathname.startsWith(`/${locale}/`) || pathname === `/${locale}`
  )

  if (pathnameHasLocale) {
    // Example protected routes (expand as needed)
    if (pathname.match(/^\/(en|ar)\/dashboard/)) {
      if (!session) {
        const locale = pathname.split('/')[1] || defaultLocale
        const loginUrl = new URL(`/${locale}/auth/login`, request.url)
        return NextResponse.redirect(loginUrl)
      }
    }
    return NextResponse.next()
  }

  // Check for saved language preference in cookies
  const savedLang = request.cookies.get('preferred-language')?.value
  let targetLocale = defaultLocale
  
  if (savedLang && locales.includes(savedLang)) {
    targetLocale = savedLang
  }

  // Redirect to the target locale
  const newUrl = new URL(`/${targetLocale}${pathname}`, request.url)
  const response = NextResponse.redirect(newUrl)
  
  // Set the cookie if it doesn't exist
  if (!savedLang) {
    response.cookies.set('preferred-language', targetLocale, {
      maxAge: 60 * 60 * 24 * 365, // 1 year
      path: '/',
    })
  }
  
  return response
}

export const config = {
  matcher: [
    // Skip all internal paths (_next), API routes, static assets, and favicon
    '/((?!_next|api|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp|ico|css|js|woff|woff2|ttf|eot)).*)',
  ],
} 