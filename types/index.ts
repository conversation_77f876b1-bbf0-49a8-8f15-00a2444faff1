// Types that match the Convex backend schema exactly

export interface Doctor {
  _id: string
  // Basic Information
  firstName?: string
  lastName?: string
  email?: string
  password?: string
  
  // Professional Information
  professionalTitle?: string
  specialty: string
  practiceType?: "clinic-hospital" | "telemedicine"
  clinicName?: string
  address?: string
  
  // Working Hours
  workingHours?: Record<string, {
    enabled: boolean
    start: string
    end: string
  }>
  
  // Contact & Additional Info
  phoneNumber?: string
  notes?: string
  
  // Legacy fields for backward compatibility
  name?: string
  title?: string
  phones?: Array<{
    raw: string
    e164?: string
    is_whatsapp?: boolean
    label?: string
  }>
  clinic_name?: string
  working_hours?: string | null
  image?: string
  sources?: string[]
  
  // Timestamps
  createdAt?: number
  updatedAt?: number
}

export interface Patient {
  _id: string
  firstName: string
  lastName: string
  email: string
  password: string
  userType: "patient" | "doctor"
  createdAt: number
  updatedAt: number
}

export interface Clinic {
  id: string
  name: string
  specialties: string[]
  rating: number
  reviewCount: number
  location: string
  image: string
  doctorCount: number
}

export interface Specialty {
  id: string
  name: string
  doctorCount: number
  icon: string
}

export interface City {
  id: string
  name: string
  doctorCount: number
}

export interface Area {
  id: string
  name: string
}

export interface WorkingHours {
  enabled: boolean
  start: string
  end: string
}

// Helper type for doctor display (combines new and legacy fields)
export interface DoctorDisplay extends Doctor {
  displayName: string
  displayTitle: string
  displayClinic: string
  displayWorkingHours: string
  displayPhone: string
}
