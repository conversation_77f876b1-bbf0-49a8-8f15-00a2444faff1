import { type ClassValue, clsx } from "clsx"
import { twMerge } from "tailwind-merge"
import { Doctor, DoctorDisplay } from "@/types"

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

// Transform backend doctor data to frontend display format
export function transformDoctorToDisplay(doctor: Doctor): DoctorDisplay {
  return {
    ...doctor,
    displayName: doctor.name || `${doctor.firstName || ''} ${doctor.lastName || ''}`.trim() || 'Unknown Doctor',
    displayTitle: doctor.title || doctor.professionalTitle || 'Doctor',
    displayClinic: doctor.clinic_name || doctor.address || 'Location not specified',
    displayWorkingHours: formatWorkingHours(doctor.workingHours, doctor.working_hours),
    displayPhone: doctor.phones?.[0]?.raw || 'Phone not available'
  }
}

// Format working hours from either new structured format or legacy string format
function formatWorkingHours(
  workingHours?: Record<string, { enabled: boolean; start: string; end: string }>,
  legacyWorkingHours?: string | null
): string {
  if (workingHours) {
    const enabledDays = Object.entries(workingHours)
      .filter(([_, hours]) => hours.enabled)
      .map(([day, hours]) => `${day}: ${hours.start}-${hours.end}`)
      .join(', ')
    return enabledDays || 'Working hours not specified'
  }
  
  if (legacyWorkingHours) {
    return legacyWorkingHours
  }
  
  return 'Working hours not specified'
}

// Helper function to get doctor's full name
export function getDoctorFullName(doctor: Doctor): string {
  if (doctor.name) return doctor.name
  if (doctor.firstName && doctor.lastName) return `${doctor.firstName} ${doctor.lastName}`
  if (doctor.firstName) return doctor.firstName
  if (doctor.lastName) return doctor.lastName
  return 'Unknown Doctor'
}

// Helper function to get doctor's title
export function getDoctorTitle(doctor: Doctor): string {
  return doctor.title || doctor.professionalTitle || 'Doctor'
}

// Helper function to get doctor's clinic name
export function getDoctorClinic(doctor: Doctor): string {
  return doctor.clinicName || doctor.clinic_name || doctor.address || 'Location not specified'
}

// Helper function to get doctor's phone number
export function getDoctorPhone(doctor: Doctor): string {
  return doctor.phones?.[0]?.raw || 'Phone not available'
}

// Locale-aware routing utilities
export function getLocalePath(path: string, locale: string): string {
  // Remove leading slash if present
  const cleanPath = path.startsWith('/') ? path.slice(1) : path
  return `/${locale}/${cleanPath}`
}

export function getPathWithoutLocale(pathname: string): string {
  // Remove locale prefix from pathname
  return pathname.replace(/^\/[a-z]{2}/, '') || '/'
}

export function isValidLocale(locale: string): locale is 'en' | 'ar' {
  return ['en', 'ar'].includes(locale)
}
