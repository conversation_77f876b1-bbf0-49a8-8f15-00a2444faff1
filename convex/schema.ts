import { defineSchema, defineTable } from "convex/server";
import { v } from "convex/values";

export default defineSchema({
  patients: defineTable({
    firstName: v.string(),
    lastName: v.string(),
    email: v.string(),
    phone: v.string(),
    password: v.string(),
    userType: v.union(v.literal("patient"), v.literal("doctor")),
    createdAt: v.number(),
    updatedAt: v.number()
  })
    .index("by_email", ["email"])
    .index("by_userType", ["userType"]),

  doctors: defineTable({
    // Basic Information
    firstName: v.optional(v.string()),
    lastName: v.optional(v.string()),
    email: v.optional(v.string()),
    password: v.optional(v.string()),
    
    // Professional Information
    professionalTitle: v.optional(v.string()),
    specialty: v.string(),
    practiceType: v.optional(v.union(v.literal("clinic-hospital"), v.literal("telemedicine"))),
    clinic_name: v.optional(v.string()),
    address: v.optional(v.string()),
    
    // Working Hours
    workingHours: v.optional(v.record(
      v.string(),
      v.object({
        enabled: v.boolean(),
        start: v.string(),
        end: v.string()
      })
    )),
    
    // Contact & Additional Info
    phones: v.optional(v.array(
      v.object({
        raw: v.string(),
        e164: v.optional(v.string()),
        is_whatsapp: v.optional(v.boolean()),
        label: v.optional(v.string())
      })
    )),
    notes: v.optional(v.string()),
    
    // Temporary: Keep old fields for migration
    clinicName: v.optional(v.string()),
    phoneNumber: v.optional(v.string()),
    
    // Legacy fields for backward compatibility
    name: v.optional(v.string()),
    title: v.optional(v.string()),
    working_hours: v.optional(v.union(v.string(), v.null())),
    image: v.optional(v.string()),
    sources: v.optional(v.array(v.string())),
    
    // Timestamps
    createdAt: v.optional(v.number()),
    updatedAt: v.optional(v.number())
  })
    .index("by_email", ["email"])
    .index("by_specialty", ["specialty"])
    .index("by_practiceType", ["practiceType"])
});
