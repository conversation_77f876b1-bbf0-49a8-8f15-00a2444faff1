import { mutation } from "./_generated/server";

// Migration to add phone field to existing patients
export const addPhoneToExistingPatients = mutation({
  args: {},
  handler: async ({ db }) => {
    try {
      // Get all patients without phone field
      const patients = await db.query("patients").collect();
      
      let updatedCount = 0;
      for (const patient of patients) {
        if (!patient.phone) {
          // Update patient with default phone number
          await db.patch(patient._id, {
            phone: "Not provided"
          });
          updatedCount++;
        }
      }
      
      console.log(`Updated ${updatedCount} patients with default phone number`);
      return { success: true, updatedCount };
    } catch (error) {
      console.error("Migration failed:", error);
      throw error;
    }
  }
});

// Migration to update doctor field names
export const updateDoctorFieldNames = mutation({
  args: {},
  handler: async ({ db }) => {
    try {
      // Get all doctors
      const doctors = await db.query("doctors").collect();
      
      let updatedCount = 0;
      for (const doctor of doctors) {
        const updates: any = {};
        
        // Convert clinicName to clinic_name
        if ((doctor as any).clinicName && !doctor.clinic_name) {
          updates.clinic_name = (doctor as any).clinicName;
        }
        
        // Convert phoneNumber to phones array
        if ((doctor as any).phoneNumber && !doctor.phones) {
          updates.phones = [{ raw: (doctor as any).phoneNumber, label: 'primary' }];
        }
        
        // Only update if there are changes
        if (Object.keys(updates).length > 0) {
          await db.patch(doctor._id, updates);
          updatedCount++;
        }
      }
      
      console.log(`Updated ${updatedCount} doctors with new field names`);
      return { success: true, updatedCount };
    } catch (error) {
      console.error("Migration failed:", error);
      throw error;
    }
  }
});
