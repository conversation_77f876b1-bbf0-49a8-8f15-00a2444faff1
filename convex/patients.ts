import { mutation, query } from "./_generated/server";
import { v } from "convex/values";

// Register a new patient
export const registerPatient = mutation({
  args: {
    firstName: v.string(),
    lastName: v.string(),
    email: v.string(),
    phone: v.string(),
    password: v.string(),
    userType: v.literal("patient")
  },
  handler: async ({ db }, { firstName, lastName, email, phone, password, userType }) => {
    try {
      // Check if patient already exists
      const existingPatient = await db
        .query("patients")
        .withIndex("by_email", q => q.eq("email", email))
        .first();

      if (existingPatient) {
        throw new Error("Patient with this email already exists");
      }

      // Check if doctor already exists with this email
      const existingDoctor = await db
        .query("doctors")
        .withIndex("by_email", q => q.eq("email", email))
        .first();

      if (existingDoctor) {
        throw new Error("A doctor account already exists with this email");
      }

      // Create new patient (password already hashed by action)
      const patientId = await db.insert("patients", {
        firstName,
        lastName,
        email,
        phone,
        password,
        userType,
        createdAt: Date.now(),
        updatedAt: Date.now()
      });

      return { success: true, patientId };
    } catch (error) {
      console.error("Error registering patient:", error);
      throw error;
    }
  }
});

// Get patient by email
export const getPatientByEmail = query({
  args: { email: v.string() },
  handler: async ({ db }, { email }) => {
    try {
      const patient = await db
        .query("patients")
        .withIndex("by_email", q => q.eq("email", email))
        .first();
      
      return patient;
    } catch (error) {
      console.error("Error fetching patient by email:", error);
      return null;
    }
  }
});
