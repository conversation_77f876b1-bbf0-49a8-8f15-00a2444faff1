import { action } from "./_generated/server";
import { v } from "convex/values";
import { api } from "./_generated/api";
import bcrypt from "bcryptjs";

export const validateLogin = action({
  args: {
    role: v.union(v.literal("patient"), v.literal("doctor")),
    email: v.string(),
    password: v.string()
  },
  handler: async (ctx, { role, email, password }): Promise<{ success: boolean; user?: any; error?: string }> => {
    try {
      if (role === "patient") {
        const patient = await ctx.runQuery(api.patients.getPatientByEmail, { email });
        if (!patient) {
          return { success: false, error: "Patient not found" };
        }
        
        const isValidPassword = await bcrypt.compare(password, patient.password as string);
        if (!isValidPassword) {
          return { success: false, error: "Invalid password" };
        }
        
        return { success: true, user: patient };
      } else if (role === "doctor") {
        const doctor = await ctx.runQuery(api.doctors.getDoctorByEmail, { email });
        if (!doctor) {
          return { success: false, error: "Doctor not found" };
        }
        
        const isValidPassword = await bcrypt.compare(password, doctor.password as string);
        if (!isValidPassword) {
          return { success: false, error: "Invalid password" };
        }
        
        return { success: true, user: doctor };
      }
      
      return { success: false, error: "Invalid role" };
    } catch (error) {
      return { success: false, error: "Authentication failed" };
    }
  },
});

export const registerPatient = action({
  args: {
    firstName: v.string(),
    lastName: v.string(),
    email: v.string(),
    phone: v.string(),
    password: v.string(),
    userType: v.literal("patient")
  },
  handler: async (ctx, args): Promise<{ success: boolean; patientId?: any; error?: string }> => {
    try {
      // Hash the password using bcrypt (allowed in actions)
      const hashedPassword = await bcrypt.hash(args.password, 10);
      
      // Insert the patient with hashed password using the mutation
      const patientId = await ctx.runMutation(api.patients.registerPatient, {
        ...args,
        password: hashedPassword,
      });
      
      return { success: true, patientId };
    } catch (error: any) {
      console.error("Patient registration error:", error);
      throw new Error(error?.message || "Registration failed");
    }
  },
});

export const registerDoctor = action({
  args: {
    firstName: v.string(),
    lastName: v.string(),
    email: v.string(),
    password: v.string(),
    professionalTitle: v.string(),
    specialty: v.string(),
    practiceType: v.union(v.literal("clinic-hospital"), v.literal("telemedicine")),
    clinic_name: v.optional(v.string()),
    address: v.optional(v.string()),
    workingHours: v.optional(v.record(
      v.string(),
      v.object({
        enabled: v.boolean(),
        start: v.string(),
        end: v.string()
      })
    )),
    phones: v.optional(v.array(
      v.object({
        raw: v.string(),
        e164: v.optional(v.string()),
        is_whatsapp: v.optional(v.boolean()),
        label: v.optional(v.string())
      })
    )),
    notes: v.optional(v.string())
  },
  handler: async (ctx, args): Promise<{ success: boolean; doctorId?: any; error?: string }> => {
    try {
      // Hash the password using bcrypt (allowed in actions)
      const hashedPassword = await bcrypt.hash(args.password, 10);
      
      // Insert the doctor with hashed password using the mutation
      const doctorId = await ctx.runMutation(api.doctors.registerDoctor, {
        ...args,
        password: hashedPassword,
      });
      
      return { success: true, doctorId };
    } catch (error: any) {
      console.error("Doctor registration error:", error);
      throw new Error(error?.message || "Registration failed");
    }
  },
});


