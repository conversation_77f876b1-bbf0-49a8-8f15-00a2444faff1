import { mutation, query } from "./_generated/server";
import { v } from "convex/values";
import bcrypt from "bcryptjs";

// Register a new doctor
export const registerDoctor = mutation({
  args: {
    firstName: v.string(),
    lastName: v.string(),
    email: v.string(),
    password: v.string(),
    professionalTitle: v.string(),
    specialty: v.string(),
    practiceType: v.union(v.literal("clinic-hospital"), v.literal("telemedicine")),
    clinic_name: v.optional(v.string()),
    address: v.optional(v.string()),
    workingHours: v.optional(v.record(
      v.string(),
      v.object({
        enabled: v.boolean(),
        start: v.string(),
        end: v.string()
      })
    )),
    phones: v.optional(v.array(
      v.object({
        raw: v.string(),
        e164: v.optional(v.string()),
        is_whatsapp: v.optional(v.boolean()),
        label: v.optional(v.string())
      })
    )),
    notes: v.optional(v.string())
  },
  handler: async ({ db }, args) => {
    try {
      // Check if doctor already exists
      const existingDoctor = await db
        .query("doctors")
        .withIndex("by_email", q => q.eq("email", args.email))
        .first();

      if (existingDoctor) {
        throw new Error("Doctor with this email already exists");
      }

      // Check if patient already exists with this email
      const existingPatient = await db
        .query("patients")
        .withIndex("by_email", q => q.eq("email", args.email))
        .first();

      if (existingPatient) {
        throw new Error("A patient account already exists with this email");
      }

      // Create new doctor (password already hashed by action)
      const doctorId = await db.insert("doctors", {
        firstName: args.firstName,
        lastName: args.lastName,
        email: args.email,
        password: args.password,
        professionalTitle: args.professionalTitle,
        specialty: args.specialty,
        practiceType: args.practiceType,
        clinic_name: args.clinic_name,
        address: args.address,
        workingHours: args.workingHours,
        phones: args.phones,
        notes: args.notes,
        createdAt: Date.now(),
        updatedAt: Date.now()
      });

      return { success: true, doctorId };
    } catch (error) {
      console.error("Error registering doctor:", error);
      throw error;
    }
  }
});



// Bulk upsert for importing doctor data
export const bulkUpsert = mutation({
  args: { 
    items: v.array(
      v.object({
        // Legacy fields for backward compatibility
        name: v.optional(v.string()),
        specialty: v.string(),
        title: v.optional(v.string()),
        phones: v.optional(v.array(
          v.object({
            raw: v.string(),
            e164: v.optional(v.string()),
            is_whatsapp: v.optional(v.boolean()),
            label: v.optional(v.string())
          })
        )),
        email: v.optional(v.string()),
        clinic_name: v.optional(v.string()),
        address: v.optional(v.string()),
        working_hours: v.optional(v.union(v.string(), v.null())),
        notes: v.optional(v.string()),
        image: v.optional(v.string()),
        sources: v.optional(v.array(v.string())),
        
        // New fields
        firstName: v.optional(v.string()),
        lastName: v.optional(v.string()),
        password: v.optional(v.string()),
        professionalTitle: v.optional(v.string()),
        practiceType: v.optional(v.union(v.literal("clinic-hospital"), v.literal("telemedicine"))),
        clinicName: v.optional(v.string()),
        workingHours: v.optional(v.record(
          v.string(),
          v.object({
            enabled: v.boolean(),
            start: v.string(),
            end: v.string()
          })
        )),
        phoneNumber: v.optional(v.string()),
        createdAt: v.optional(v.number()),
        updatedAt: v.optional(v.number())
      })
    ) 
  },
  handler: async ({ db }, { items }) => {
    for (const doc of items) {
              // Clean the data to handle null values and ensure required fields
        const cleanDoc = {
          // Required fields with defaults
          firstName: doc.firstName || doc.name?.split(' ')[0] || 'Unknown',
          lastName: doc.lastName || doc.name?.split(' ').slice(1).join(' ') || 'Unknown',
          email: doc.email || `unknown-${Date.now()}@temp.com`,
          password: doc.password || 'temp-password',
          professionalTitle: doc.professionalTitle || doc.title || 'doctor',
          specialty: doc.specialty,
          practiceType: doc.practiceType || 'clinic-hospital',
          phones: doc.phones || (doc.phoneNumber ? [{ raw: doc.phoneNumber }] : undefined),
          
          // Optional fields
          clinic_name: doc.clinic_name || doc.clinicName,
          address: doc.address,
          workingHours: doc.workingHours,
          notes: doc.notes,
          image: doc.image,
        
                  // Legacy fields for backward compatibility
          name: doc.name,
          title: doc.title,
          working_hours: doc.working_hours === null ? undefined : doc.working_hours,
          sources: doc.sources,
        
        // Timestamps
        createdAt: doc.createdAt || Date.now(),
        updatedAt: doc.updatedAt || Date.now()
      };

      // Try to find existing doctor by email first, then by name
      let existing = null;
      if (doc.email) {
        existing = await db
          .query("doctors")
          .withIndex("by_email", q => q.eq("email", doc.email!))
          .first();
      }
      
      if (!existing && doc.name) {
        existing = await db
          .query("doctors")
          .filter(q => q.eq(q.field("firstName"), cleanDoc.firstName) && q.eq(q.field("lastName"), cleanDoc.lastName))
          .first();
      }

      if (existing) {
        await db.patch(existing._id, cleanDoc);
      } else {
        await db.insert("doctors", cleanDoc);
      }
    }
  }
});

// Query to get featured doctors (first 6 doctors)
export const getFeaturedDoctors = query({
  args: {},
  handler: async ({ db }) => {
    try {
      const doctors = await db
        .query("doctors")
        .order("desc")
        .take(6);
      
      return doctors;
    } catch (error) {
      console.error("Error fetching featured doctors:", error);
      return [];
    }
  }
});

// Query to get all doctors
export const getAllDoctors = query({
  args: {},
  handler: async ({ db }) => {
    try {
      const doctors = await db
        .query("doctors")
        .order("desc")
        .collect();
      
      return doctors;
    } catch (error) {
      console.error("Error fetching all doctors:", error);
      return [];
    }
  }
});

// Query to get doctors by specialty
export const getDoctorsBySpecialty = query({
  args: { specialty: v.string() },
  handler: async ({ db }, { specialty }) => {
    const doctors = await db
      .query("doctors")
      .withIndex("by_specialty", q => q.eq("specialty", specialty))
      .collect();
    
    return doctors;
  }
});

// Query to get doctors by practice type
export const getDoctorsByPracticeType = query({
  args: { practiceType: v.union(v.literal("clinic-hospital"), v.literal("telemedicine")) },
  handler: async ({ db }, { practiceType }) => {
    const doctors = await db
      .query("doctors")
      .withIndex("by_practiceType", q => q.eq("practiceType", practiceType))
      .collect();
    
    return doctors;
  }
});

// Query to get a single doctor by ID
export const getDoctorById = query({
  args: { id: v.id("doctors") },
  handler: async ({ db }, { id }) => {
    try {
      const doctor = await db.get(id);
      return doctor;
    } catch (error) {
      console.error("Error fetching doctor by ID:", error);
      return null;
    }
  }
});

// Query to get doctor by email
export const getDoctorByEmail = query({
  args: { email: v.string() },
  handler: async ({ db }, { email }) => {
    try {
      const doctor = await db
        .query("doctors")
        .withIndex("by_email", q => q.eq("email", email))
        .first();
      
      return doctor;
    } catch (error) {
      console.error("Error fetching doctor by email:", error);
      return null;
    }
  }
});

// Update doctor profile
export const updateDoctor = mutation({
  args: {
    id: v.id("doctors"),
    updates: v.object({
      firstName: v.optional(v.string()),
      lastName: v.optional(v.string()),
      email: v.optional(v.string()),
      professionalTitle: v.optional(v.string()),
      specialty: v.optional(v.string()),
      practiceType: v.optional(v.union(v.literal("clinic-hospital"), v.literal("telemedicine"))),
      clinicName: v.optional(v.string()),
      address: v.optional(v.string()),
      workingHours: v.optional(v.record(
        v.string(),
        v.object({
          enabled: v.boolean(),
          start: v.string(),
          end: v.string()
        })
      )),
      phoneNumber: v.optional(v.string()),
      notes: v.optional(v.string()),
      image: v.optional(v.string()),
      updatedAt: v.number()
    })
  },
  handler: async ({ db }, { id, updates }) => {
    try {
      await db.patch(id, updates);
      return { success: true };
    } catch (error) {
      console.error("Error updating doctor:", error);
      throw error;
    }
  }
});
