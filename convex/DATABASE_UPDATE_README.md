# Database Schema Update for Healthcare Booking Platform

## Overview
This update introduces a new database schema to support the enhanced signup forms for both patients and doctors, with improved data structure and new functionality.

## New Schema Structure

### 1. Users Table
- **Purpose**: Stores user accounts for both patients and doctors
- **Fields**:
  - `firstName`, `lastName`: User's full name
  - `email`: Unique email address
  - `password`: User password (should be hashed in production)
  - `userType`: Either "patient" or "doctor"
  - `createdAt`, `updatedAt`: Timestamps

### 2. Doctors Table (Updated)
- **Purpose**: Stores detailed doctor profiles
- **New Fields**:
  - `firstName`, `lastName`: Doctor's full name
  - `professionalTitle`: Professional title (Dr., Consultant, Professor, Specialist)
  - `practiceType`: Either "clinic-hospital" or "telemedicine"
  - `clinicName`: Name of clinic/hospital (optional for telemedicine)
  - `address`: Physical address (optional for telemedicine)
  - `workingHours`: Structured working hours with day-by-day configuration
  - `phoneNumber`: Contact phone number
  - `notes`: Additional information about the doctor

## New Functions

### User Management (`convex/users.ts`)
- `registerPatient()`: Register new patient accounts
- `registerDoctor()`: Register new doctor accounts
- `getUserByEmail()`: Find user by email
- `getUserById()`: Find user by ID
- `getUsersByType()`: Get all users of a specific type
- `updateUser()`: Update user profile information

### Enhanced Doctor Management (`convex/doctors.ts`)
- `getDoctorsByPracticeType()`: Filter doctors by practice type
- `getDoctorByEmail()`: Find doctor by email
- `updateDoctor()`: Update doctor profile information
- Enhanced `bulkUpsert()`: Now handles both old and new schema formats

### Migration Functions (`convex/migrations.ts`)
- `migrateDoctorsToNewSchema()`: Update existing doctors to new schema
- `createUsersForExistingDoctors()`: Create user accounts for existing doctors
- `cleanupOldFields()`: Remove deprecated fields after migration

## How to Use

### 1. Deploy the New Schema
```bash
npx convex dev
```

### 2. Run Migrations (in order)
```typescript
// First, migrate existing doctors to new schema
await convex.mutation("migrations:migrateDoctorsToNewSchema")();

// Then, create user accounts for existing doctors
await convex.mutation("migrations:createUsersForExistingDoctors")();

// Finally, clean up old fields (optional)
await convex.mutation("migrations:cleanupOldFields")();
```

### 3. Use New Functions in Your App
```typescript
// Register a new patient
const patientResult = await convex.mutation("patients:registerPatient")({
  firstName: "John",
  lastName: "Doe",
  email: "<EMAIL>",
  password: "securepassword"
});

// Register a new doctor
const doctorResult = await convex.mutation("doctors:registerDoctor")({
  firstName: "Dr. Jane",
  lastName: "Smith",
  email: "<EMAIL>",
  password: "securepassword",
  professionalTitle: "consultant",
  specialty: "cardiology",
  practiceType: "clinic-hospital",
  clinicName: "Heart Care Clinic",
  address: "123 Medical Center Dr",
  phoneNumber: "+**********",
  workingHours: {
    "Monday": { enabled: true, start: "09:00", end: "17:00" },
    "Tuesday": { enabled: true, start: "09:00", end: "17:00" },
    // ... other days
  },
  notes: "Specializes in interventional cardiology"
});

// Get doctors by practice type
const clinicDoctors = await convex.query("doctors:getDoctorsByPracticeType")({
  practiceType: "clinic-hospital"
});
```

## Working Hours Structure
The new `workingHours` field uses a structured format:
```typescript
{
  "Sunday": { enabled: false, start: "", end: "" },
  "Monday": { enabled: true, start: "09:00", end: "17:00" },
  "Tuesday": { enabled: true, start: "09:00", end: "17:00" },
  // ... etc
}
```

## Backward Compatibility
- All existing doctor data will continue to work
- Legacy fields (`name`, `title`, `phones`, etc.) are preserved
- New functions automatically map old data to new format
- Gradual migration is supported

## Security Notes
- Passwords should be hashed before storing in production
- Consider implementing proper authentication middleware
- Validate all input data before processing

## Next Steps
1. Test the new functions with sample data
2. Update your frontend to use the new registration functions
3. Run migrations on your production database
4. Monitor for any issues during the transition
5. Clean up old fields once you're confident everything works

## Support
If you encounter any issues during the migration, check the Convex dashboard for error logs and ensure all functions are properly deployed.
